// ProfileDetails.jsx
import React, { useState } from "react";
import { Mail, Phone } from "lucide-react";
import { HiChevronDown, HiChevronUp } from "react-icons/hi";

const ProfileDetails = ({
  candidateProf,
  strippedResumeJson,
  detailsBlur,
  setSubPopup,
  questionsAndAnswers, // Added prop
}) => {
  const [skillsExpanded, setSkillsExpanded] = useState(false);

  // TEMP LOG: Confirm questionsAndAnswers prop
  console.log("ProfileDetails: questionsAndAnswers prop", questionsAndAnswers);

  const skillsArray = strippedResumeJson && Array.isArray(strippedResumeJson.skills)
    ? strippedResumeJson.skills
    : [];

  return (
  <div>
    {/* Modern Collapsible Skills Section */}
    <section className="rounded-2xl bg-gradient-to-br from-white via-gray-50 to-gray-100 border border-gray-200 shadow-xl p-6 transition-all duration-200 hover:shadow-2xl">
      {/* Skills Header with Toggle */}
      <button
        onClick={() => setSkillsExpanded(!skillsExpanded)}
        className="w-full flex items-center justify-between mb-4 p-3 rounded-lg bg-gradient-to-r from-indigo-50 to-purple-50 hover:from-indigo-100 hover:to-purple-100 border border-indigo-200 transition-all duration-200"
      >
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg shadow-sm">
            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
              <circle cx="10" cy="10" r="10" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-gray-900">Skills</h2>
          <span className="text-sm bg-indigo-200 text-indigo-800 px-3 py-1 rounded-full font-semibold">
            {skillsArray.length} skills
          </span>
        </div>
        {skillsExpanded ? (
          <HiChevronUp className="w-5 h-5 text-indigo-600" />
        ) : (
          <HiChevronDown className="w-5 h-5 text-indigo-600" />
        )}
      </button>

      {/* Collapsible Skills Content */}
      <div className={`transition-all duration-300 ease-in-out overflow-hidden ${
        skillsExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
      }`}>
        <div className="flex flex-wrap gap-3">
          {skillsArray.map((skill, index) => (
            <span
              key={index}
              className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 px-4 py-2 text-sm font-semibold text-indigo-700 border border-indigo-200 shadow-sm hover:shadow-md transition-all duration-200 transform hover:-translate-y-0.5"
            >
              <svg className="w-3 h-3 text-indigo-400" fill="currentColor" viewBox="0 0 20 20">
                <circle cx="10" cy="10" r="10" />
              </svg>
              {skill.trim()}
            </span>
          ))}
        </div>
      </div>
    </section>

    {/* Modern Experience Section */}
    <div className="space-y-4 rounded-2xl bg-gradient-to-br from-white via-gray-50 to-gray-100 border border-gray-200 shadow-xl p-6 transition-all duration-200 hover:shadow-2xl">
      <div className="flex items-center gap-3 mb-6">
        <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg shadow-sm">
          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
            <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
          </svg>
        </div>
        <h2 className="text-xl font-bold text-gray-900">
          Professional Experience
        </h2>
      </div>
      <div className="space-y-6">
        {strippedResumeJson &&
          strippedResumeJson.workExperience.map((experience, index) => (
            <div key={index} className="bg-white rounded-xl p-5 border border-gray-200 shadow-md hover:shadow-lg transition-all duration-200">
              <div className="flex items-start space-x-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-xl font-bold text-white shadow-md">
                  {experience.company[0]}
                </div>
                <div className="flex flex-col space-y-2 flex-grow">
                  <div className="flex flex-wrap items-center gap-3">
                    <h3 className="text-lg font-bold text-gray-900">
                      {experience.company}
                    </h3>
                    <span className="inline-flex items-center gap-1 rounded-full bg-blue-100 px-3 py-1 text-sm font-semibold text-blue-700 border border-blue-200">
                      <svg className="w-3 h-3 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                      </svg>
                      {experience.dates}
                    </span>
                  </div>
                  <p className="text-md font-semibold text-indigo-700 bg-indigo-50 px-3 py-1 rounded-lg inline-block">
                    {experience.title}
                  </p>
                  <div className="bg-gray-50 rounded-lg p-3">
                    <ul className="space-y-2 text-sm text-gray-700">
                      {experience.responsibilities.map(
                        (responsibility, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <span className="w-1.5 h-1.5 bg-indigo-500 rounded-full mt-2 flex-shrink-0"></span>
                            <span>{responsibility}</span>
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          ))}
      </div>
    </div>

    {/* Modern Education Section */}
    <div className="space-y-4 rounded-2xl bg-gradient-to-br from-white via-gray-50 to-gray-100 border border-gray-200 shadow-xl p-6 transition-all duration-200 hover:shadow-2xl">
      <div className="flex items-center gap-3 mb-6">
        <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg shadow-sm">
          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
          </svg>
        </div>
        <h2 className="text-xl font-bold text-gray-900">Education</h2>
      </div>
      <div className="space-y-4">
        {strippedResumeJson &&
          strippedResumeJson.education.map((edu, index) => (
            <div key={index} className="bg-white rounded-xl p-5 border border-gray-200 shadow-md hover:shadow-lg transition-all duration-200">
              <div className="flex items-start space-x-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-purple-500 to-pink-600 text-xl font-bold text-white shadow-md">
                  {edu.institution[0]}
                </div>
                <div className="flex flex-col space-y-2 flex-grow">
                  <div className="flex flex-wrap items-center gap-3">
                    <h3 className="text-lg font-bold text-gray-900">
                      {edu.institution}
                    </h3>
                    <span className="inline-flex items-center gap-1 rounded-full bg-purple-100 px-3 py-1 text-sm font-semibold text-purple-700 border border-purple-200">
                      <svg className="w-3 h-3 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                      </svg>
                      Full-Time
                    </span>
                  </div>
                  <p className="text-md font-semibold text-purple-700 bg-purple-50 px-3 py-1 rounded-lg inline-block">
                    {edu.degree}
                  </p>
                  <p className="text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded-lg">
                    {edu.dates} - {edu.location || edu.coursework}
                  </p>
                </div>
              </div>
            </div>
          ))}
      </div>
    </div>

    {/* Modern Personal Information Section */}
    <div className="space-y-4 rounded-2xl bg-gradient-to-br from-white via-gray-50 to-gray-100 border border-gray-200 shadow-xl p-6 transition-all duration-200 hover:shadow-2xl">
      <div className="flex items-center gap-3 mb-6">
        <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg shadow-sm">
          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
          </svg>
        </div>
        <h2 className="text-xl font-bold text-gray-900">Personal Information</h2>
      </div>
      <div className="relative">
        <div
          className={`transition-all duration-200 ${
            detailsBlur ? "blur-md" : ""
          }`}
        >
          <div className="bg-white rounded-xl p-5 border border-gray-200 shadow-md">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg shadow-sm">
                  <Mail className="h-4 w-4 text-white" />
                </div>
                <div>
                  <p className="text-xs font-medium text-blue-700 mb-1">Email Address</p>
                  <p className="text-sm font-semibold text-gray-900">{candidateProf.cand_email}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
                <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg shadow-sm">
                  <Phone className="h-4 w-4 text-white" />
                </div>
                <div>
                  <p className="text-xs font-medium text-green-700 mb-1">Phone Number</p>
                  <p className="text-sm font-semibold text-gray-900">{candidateProf.cand_mobile}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        {detailsBlur && (
          <button
            onClick={() => setSubPopup(true)}
            className="absolute left-1/2 top-1/2 flex -translate-x-1/2 -translate-y-1/2 transform items-center space-x-2 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 px-6 py-3 font-bold text-white shadow-lg hover:shadow-xl transition-all duration-200 hover:-translate-y-0.5"
            aria-label="Unlock candidate details"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
            </svg>
            <span>Unlock Details</span>
          </button>
        )}
      </div>
    </div>

    {/* Projects */}
    <div className="space-y-4 rounded-xl bg-white p-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-800">Projects</h2>
      </div>
      <div className="space-y-5">
        {strippedResumeJson &&
          Array.isArray(strippedResumeJson.projects) &&
          strippedResumeJson.projects.map((project, index) => (
            <div key={index} className="space-y-3">
              <div className="flex items-start space-x-4">
                <div className="flex flex-col space-y-1">
                  <div className="flex items-center space-x-2">
                    <h3 className="text-lg font-semibold text-gray-800">
                      {project.name}
                    </h3>
                  </div>
                  <p className="text-sm text-gray-600">
                    {project.description}
                  </p>
                  <div className="flex flex-wrap space-x-2">
                    {Array.isArray(project.technologies) &&
                      project.technologies.map((tech, idx) => (
                        <span
                          key={idx}
                          className="rounded-lg bg-brand-500 px-2 py-1 text-xs font-thin text-white"
                        >
                          {tech}
                        </span>
                      ))}
                  </div>
                </div>
              </div>
              {index < strippedResumeJson.projects.length - 1 && (
                <hr className="mt-3 border-t border-gray-200" />
              )}
            </div>
          ))}
      </div>
    </div>

    {/* Languages */}
    <section className="rounded-2xl bg-white p-6 ">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Languages</h2>
      </div>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
        {candidateProf.languages_known
          .split(",")
          .map((lang) => (
            <div
              key={lang.slice(0, 2).toUpperCase()}
              className="flex items-center space-x-3 rounded-lg border border-b-gray-200 bg-white p-2"
            >
              <div className="flex h-9 w-9 items-center justify-center rounded-full bg-brand-600 p-2  text-white">
                <span className="text-md font-semibold">
                  {lang.slice(0, 2).toUpperCase()}
                </span>
              </div>
              <div>
                <p className="font-sm ">{lang}</p>
              </div>
            </div>
          ))}
      </div>
    </section>
    {/* Questions and Answers Section removed and moved to CandidateProfile.jsx */}
  </div>
  );
};

export default ProfileDetails;