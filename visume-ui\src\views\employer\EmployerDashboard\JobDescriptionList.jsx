
import React, { useEffect, useState, useImperative<PERSON>andle, forwardRef } from "react";
import { useNavigate } from "react-router-dom";

const JobDescriptionList = forwardRef(({ emp_id }, ref) => {
  const [jobDescriptions, setJobDescriptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this job description?")) return;
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${id}`,
        { method: "DELETE" }
      );
      if (response.ok) {
        setJobDescriptions((prev) => prev.filter((jd) => jd._id !== id));
      } else {
        alert("Failed to delete job description.");
      }
    } catch {
      alert("Error deleting job description.");
    }
  };

  const fetchJobDescriptions = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/job-description/${emp_id}`
      );
      if (response.ok) {
        const data = await response.json();
        setJobDescriptions(data.jobDescriptions || []);
      }
    } catch (err) {
      setJobDescriptions([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (emp_id) fetchJobDescriptions();
  }, [emp_id]);

  useImperativeHandle(ref, () => ({
    refresh: fetchJobDescriptions
  }));

  if (loading) return <div>Loading job descriptions...</div>;
  if (!jobDescriptions.length) return <div>No job descriptions found.</div>;

  return (
    <div className="mb-6">
      <h3 className="text-lg font-semibold mb-2 text-gray-800 dark:text-white">
        All Job Descriptions
      </h3>
      <div className="max-h-96 overflow-auto border rounded-md bg-gray-50 dark:bg-navy-700 p-2">
        {jobDescriptions.map((jd, idx) => (
          <div
            key={jd._id || idx}
            className="mb-2 p-2 rounded bg-white dark:bg-navy-600 shadow-sm border cursor-pointer"
            onClick={() => navigate(`/employer/job-description/${jd._id}`, { state: { job: jd } })}
            title="View matching candidates"
          >
            <div className="text-sm font-bold text-blue-700 dark:text-blue-300">
              {jd.role || "No Role"}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-300">
              Location: {Array.isArray(jd.location) ? jd.location.join(", ") : jd.location}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-300">
              Experience: {jd.experience}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-300">
              Skills: {Array.isArray(jd.skills) ? jd.skills.join(", ") : jd.skills}
            </div>
            <button
              className="ml-2 px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 float-right"
              onClick={e => { e.stopPropagation(); handleDelete(jd._id); }}
              title="Delete"
            >
              Delete
            </button>
          </div>
        ))}
      </div>
    </div>
  );
});

export default JobDescriptionList;