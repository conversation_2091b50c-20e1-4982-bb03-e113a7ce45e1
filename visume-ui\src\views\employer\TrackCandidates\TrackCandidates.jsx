import React, { useEffect, useState } from "react";
import {
  HiOutlineShare,
  HiOutlineArchive,
  HiOutlinePencil,
  HiOutlineUpload,
  HiOutlineSearch,
  HiLockOpen,
  HiOutlineBriefcase,
  HiOutlineDownload,
  HiOutlineVideoCamera,
  HiDocumentText,
  HiOutlineDocumentText,
  HiHeart,
  HiLockClosed,
} from "react-icons/hi";
import { useLocation, useNavigate } from "react-router-dom";
import Cookies from "js-cookie";
import Loader from "components/Loader";
import toast from "react-hot-toast";

import CandidateRow from "./CandidateRow";

const TrackCandidates = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const tabName = params.get("tab");
  const [loader, setLoader] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [shortListedCandidates, setShortListedCandidates] = useState([]);
  const [unlockedData, setUnlockedData] = useState([]);
  const [activeTabName, setActiveTabName] = useState("Shortlisted");
  const [loadingId, setLoadingId] = useState(null);
  const [noShortlistedProfiles, setNoShortlistedProfiles] = useState(false);
  const handleSearchToggle = () => setShowSearch((prev) => !prev);

  const handleSearchChange = (e) => setSearchTerm(e.target.value);

  const handleStatusChange = async (video_profile_id, newStatus) => {
    if (newStatus !== "Unlocked") {
      toast.error("This function is not ready yet");
      return;
    }

    if (loadingId === video_profile_id) return;
    setLoadingId(video_profile_id);

    try {
      const emp_id = Cookies.get("employerId");
      if (!emp_id) {
        return toast.error(
          "You need to be an employer to update video profile status"
        );
      }

      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/employer-profiles/unlockVideoProfile`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ emp_id, video_profile_id }),
        }
      );

      if (!response.ok) {
        const msg = await response.json();
        return toast.error(msg.message);
      }

      const data = await response.json();
      toast.success(data.message);
      await fetchCandidates();
    } catch (error) {
      toast.error("Error updating video resume status");
      console.error("Error during updating video resume status:", error);
    } finally {
      setLoadingId(null);
    }
  };

  const handleClickOutside = (event) => setShowSearch(false);

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleUnlockCandidate = (candidateId) => {
    const updateCandidates = (prevData) =>
      prevData.map((candidate) =>
        candidate.id === candidateId
          ? { ...candidate, unlocked: !candidate.unlocked }
          : candidate
      );

    if (activeTabName === "Shortlisted") {
      setShortListedCandidates(updateCandidates);
    } else {
      setUnlockedData(updateCandidates);
    }
  };

  const filteredCandidates = (
    activeTabName === "Shortlisted"
      ? shortListedCandidates
      : activeTabName === "Unlocked"
      ? unlockedData
      : []
  ).filter((candidate) => {
    // Support both candidate.cand_name and candidate.jobseeker?.cand_name
    const name =
      candidate.cand_name ||
      (candidate.jobseeker && candidate.jobseeker.cand_name) ||
      "";
    return name.toLowerCase().includes(searchTerm.toLowerCase());
  });

  console.log("Filtered candidates for tab", activeTabName, ":", filteredCandidates);
  const [currentPage, setCurrentPage] = useState(1);
  const candidatesPerPage = 10;
  const totalPages = Math.ceil(filteredCandidates.length / candidatesPerPage);
  const currentCandidates = filteredCandidates.slice(
    (currentPage - 1) * candidatesPerPage,
    currentPage * candidatesPerPage
  );

  const handlePageChange = (page) => {
    if (page > 0 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const unShortlistCandidate = async (id, cand_id) => {
    if (loadingId === id) return;
    setLoadingId(cand_id);
    try {
      const emp_id = Cookies.get("employerId");
      if (!emp_id) {
        return toast.error(
          "You need to be an employer to UnShortlist profiles"
        );
      }
      const response = await fetch(
        `${
          import.meta.env.VITE_APP_HOST
        }/api/v1/employer-profiles/unshortlist-candidate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ emp_id, cand_id: id }),
        }
      );
      if (!response.ok) {
        const msg = await response.json();
        toast.error(msg.message);
        throw new Error(`HTTP error! status: ${msg.message}`);
      }
      const data = await response.json();
      toast.success(data.message);
      await fetchCandidates();
    } catch (error) {
      console.error("Error during shortlist operation:", error);
    } finally {
      setLoadingId(null);
    }
  };

  const fetchCandidates = async () => {
    setLoader(true);
    const emp_id = Cookies.get("employerId");
    if (!emp_id) {
      toast.error("You are not an employer");
      navigate("/");
      return;
    }

    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/shortlisted-profiles/${emp_id}`
      );
      if (response.status === 404) {
        setNoShortlistedProfiles(true);
        setShortListedCandidates([]);
        setUnlockedData([]);
        setLoader(false);
        return;
      } else {
        setNoShortlistedProfiles(false);
      }
      const data = await response.json();

      if (data.data) {
        console.log("Fetched candidates:", data.data);
        const shortlistedCandidates = data.data.filter(
          (e) => e.status === "shortlisted"
        );
        const unlockedCandidates = data.data.filter(
          (e) => e.status === "unlocked"
        );
        setShortListedCandidates(shortlistedCandidates);
        setUnlockedData(unlockedCandidates);
        if (shortlistedCandidates.length > 0) {
          setActiveTabName("Shortlisted");
        } else if (unlockedCandidates.length > 0) {
          setActiveTabName("Unlocked");
        }
      }
    } catch (err) {
      console.error("Error fetching shortlisted profiles:", err);
    } finally {
      setLoader(false);
    }
  };

  useEffect(() => {
    fetchCandidates();
  }, [navigate]);

  useEffect(() => {
    if (tabName) {
      setActiveTabName(tabName);
    }
  }, [tabName]);

  return (
    <div className="rounded-2xl bg-gradient-to-br from-white via-gray-50 to-gray-100 p-6 shadow-xl dark:bg-gradient-to-br dark:from-navy-700 dark:via-navy-800 dark:to-navy-900">
      {/* Header Section */}
      <div className="mb-8 flex flex-col md:flex-row items-center justify-between">

        {loader && <Loader text={"Fetching Shortlisted Profiles"} />}

        <div className="flex items-center space-x-4">
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg shadow-sm">
              <HiOutlineBriefcase className="w-5 h-5 text-white" />
            </div>
            <h3 className="text-sm md:text-2xl font-bold text-gray-900 dark:text-white">
              Track Candidates
            </h3>
          </div>
        </div>

        <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:items-center md:space-x-4">
          <button
            onClick={handleSearchToggle}
            className="flex items-center space-x-2 px-4 py-2 rounded-lg bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 hover:from-gray-200 hover:to-gray-300 transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5"
          >
            <HiOutlineSearch className="text-lg" />
            <span className="font-medium">Search</span>
          </button>
          {showSearch && (
            <input
              type="text"
              value={searchTerm}
              onChange={handleSearchChange}
              className="text-sm md:text-base rounded-lg border-2 border-indigo-200 px-4 py-2 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-200"
              placeholder="Search candidates..."
            />
          )}
          <button className="flex items-center space-x-2 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 px-4 py-2 text-white font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200">
            <HiOutlineDownload className="text-lg" />
            <span>Export</span>
          </button>
        </div>

      </div>

      {/* Modern Tab Navigation */}
      <div className="mb-6 bg-white dark:bg-navy-800 rounded-xl p-2 shadow-lg border border-gray-200 dark:border-navy-600">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {["Shortlisted", "Unlocked", "Interview", "Offers"].map((tab) => {
            // Determine if the tab should be disabled
            const isDisabled =
              (tab === "Shortlisted" && shortListedCandidates.length === 0) ||
              (tab === "Unlocked" && unlockedData.length === 0) ||
              tab === "Interview" ||
              tab === "Offers";

            const getTabColor = (tabName) => {
              switch(tabName) {
                case "Shortlisted": return "from-blue-500 to-blue-600";
                case "Unlocked": return "from-orange-500 to-orange-600";
                case "Interview": return "from-green-500 to-green-600";
                case "Offers": return "from-purple-500 to-purple-600";
                default: return "from-gray-500 to-gray-600";
              }
            };

            const getCount = (tabName) => {
              switch(tabName) {
                case "Shortlisted": return shortListedCandidates.length;
                case "Unlocked": return unlockedData.length;
                default: return 0;
              }
            };

            return (
              <button
                key={tab}
                className={`relative px-4 py-3 rounded-lg text-sm font-semibold transition-all duration-200 ${
                  activeTabName === tab
                    ? `bg-gradient-to-r ${getTabColor(tab)} text-white shadow-lg transform -translate-y-0.5`
                    : isDisabled
                      ? "text-gray-400 cursor-not-allowed bg-gray-100 dark:bg-navy-700"
                      : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-navy-700 hover:text-gray-900 dark:hover:text-white"
                }`}
                onClick={() => {
                  if (!isDisabled) {
                    setActiveTabName(tab);
                  }
                }}
                disabled={isDisabled}
              >
                <div className="flex items-center justify-center gap-2">
                  <span>{tab}</span>
                  <span className={`flex h-5 w-5 items-center justify-center rounded-full text-xs font-bold ${
                    activeTabName === tab
                      ? "bg-white/20 text-white"
                      : `bg-gradient-to-r ${getTabColor(tab)} text-white`
                  }`}>
                    {getCount(tab)}
                  </span>
                </div>
              </button>
            );
          })}
        </div>
      </div>

      {noShortlistedProfiles ? (
        <div className="flex flex-col items-center justify-center py-16 bg-white dark:bg-navy-800 rounded-xl shadow-lg border border-gray-200 dark:border-navy-600">
          <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-full mb-4">
            <HiOutlineBriefcase className="w-8 h-8 text-blue-500 dark:text-blue-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">No Candidates Yet</h3>
          <p className="text-gray-600 dark:text-gray-400 text-center max-w-md">
            No shortlisted profiles found. Start by shortlisting candidates using Profile Search to see them here.
          </p>
        </div>
      ) : filteredCandidates.length > 0 ? (
        <>
          {/* Modern Candidates Table Container */}
          <div className="bg-white dark:bg-navy-800 rounded-xl shadow-lg border border-gray-200 dark:border-navy-600 overflow-hidden">
            <div className="min-h-[55vh] overflow-x-auto">
              <table className="min-w-full table-auto">
                <thead className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-navy-700 dark:to-navy-800">
                  <tr className="text-left text-sm font-semibold text-gray-700 dark:text-gray-300">
                    <th className="p-4 border-b border-gray-200 dark:border-navy-600">Candidate</th>
                    <th className="p-4 border-b border-gray-200 dark:border-navy-600">Role</th>
                    <th className="p-4 border-b border-gray-200 dark:border-navy-600">Skills</th>
                    <th className="p-4 border-b border-gray-200 dark:border-navy-600">Scores</th>
                    <th className="p-4 border-b border-gray-200 dark:border-navy-600">Contact</th>
                    <th className="p-4 border-b border-gray-200 dark:border-navy-600">Status</th>
                    <th className="p-4 border-b border-gray-200 dark:border-navy-600">Actions</th>
                  </tr>
                </thead>
                <tbody className="text-sm divide-y divide-gray-200 dark:divide-navy-600">
                  {currentCandidates.map((candidate) => (
                    <CandidateRow
                      tabName={activeTabName}
                      loadingId={loadingId}
                      key={candidate?.id}
                      candidate={candidate}
                      onStatusChange={handleStatusChange}
                      onToggleUnlock={toggleUnlockCandidate}
                      unShortlistCandidate={unShortlistCandidate}
                    />
                  ))}
                </tbody>
              </table>
            </div>

            {/* Compact Integrated Pagination */}
            <div className="border-t border-gray-200 dark:border-navy-600 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-navy-700 dark:to-navy-800 px-4 py-3">
              <div className="flex items-center justify-center space-x-2">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={`px-3 py-1.5 text-sm rounded-lg font-medium transition-all duration-200 ${
                    currentPage === 1
                      ? "cursor-not-allowed text-gray-400 bg-gray-100 dark:bg-navy-700"
                      : "text-blue-600 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/40 hover:text-blue-700"
                  }`}
                >
                  Previous
                </button>

                <div className="flex space-x-1">
                  {Array.from({ length: totalPages }, (_, index) => (
                    <button
                      key={index + 1}
                      onClick={() => handlePageChange(index + 1)}
                      className={`w-8 h-8 text-sm rounded-lg font-semibold transition-all duration-200 ${
                        currentPage === index + 1
                          ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-md transform -translate-y-0.5"
                          : "bg-gray-100 dark:bg-navy-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-navy-600 hover:text-gray-900 dark:hover:text-white"
                      }`}
                    >
                      {index + 1}
                    </button>
                  ))}
                </div>

                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className={`px-3 py-1.5 text-sm rounded-lg font-medium transition-all duration-200 ${
                    currentPage === totalPages
                      ? "cursor-not-allowed text-gray-400 bg-gray-100 dark:bg-navy-700"
                      : "text-blue-600 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/40 hover:text-blue-700"
                  }`}
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        </>
      ) : (
        <div className="flex flex-col items-center justify-center py-16 bg-white dark:bg-navy-800 rounded-xl shadow-lg border border-gray-200 dark:border-navy-600">
          <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-orange-100 to-red-100 dark:from-orange-900/20 dark:to-red-900/20 rounded-full mb-4">
            <HiOutlineSearch className="w-8 h-8 text-orange-500 dark:text-orange-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">No {activeTabName} Profiles Found</h3>
          <p className="text-gray-600 dark:text-gray-400 text-center max-w-md">
            No {activeTabName.toLowerCase()} profiles found for your current search. Try adjusting your search terms or browse all candidates.
          </p>
        </div>
      )}
    </div>
  );
};


export default TrackCandidates;
