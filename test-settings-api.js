// Test script to verify settings API endpoints are working
const fetch = require('node-fetch');

const API_BASE = 'http://localhost:8000/api/v1';
const TEST_CAND_ID = 'test123';

async function testEndpoint(method, endpoint, data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };
    
    if (data) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(`${API_BASE}${endpoint}`, options);
    const result = await response.json();
    
    console.log(`✓ ${method} ${endpoint}: ${response.status} - ${result.message || JSON.stringify(result)}`);
    return { status: response.status, data: result };
  } catch (error) {
    console.log(`✗ ${method} ${endpoint}: ERROR - ${error.message}`);
    return { error: error.message };
  }
}

async function runTests() {
  console.log('🧪 Testing Settings API Endpoints...\n');
  
  // Test profile update
  await testEndpoint('PUT', `/candidate/${TEST_CAND_ID}`, {
    cand_name: 'Test User',
    cand_email: '<EMAIL>',
    cand_mobile: '1234567890'
  });
  
  // Test profile image upload endpoint (will fail without multipart, but should reach the endpoint)
  await testEndpoint('POST', '/candidate/uploadProfileImage', {});
  
  // Test resume upload endpoint
  await testEndpoint('POST', '/candidate/uploadResume', {});
  
  // Test notifications update
  await testEndpoint('PUT', `/candidate/${TEST_CAND_ID}/notifications`, {
    emailNotifications: true,
    jobAlerts: true
  });
  
  // Test privacy update
  await testEndpoint('PUT', `/candidate/${TEST_CAND_ID}/privacy`, {
    profileVisibility: 'public',
    showContactInfo: true
  });
  
  // Test password change
  await testEndpoint('PUT', '/changePassword', {
    cand_id: TEST_CAND_ID,
    password: 'oldpass',
    newPassword: 'newpass'
  });
  
  console.log('\n✅ All endpoint tests completed!');
  console.log('Note: "Candidate not found" responses are expected for test data.');
}

runTests().catch(console.error);
