const axios = require("axios");
const pdf = require("pdf-parse");
const { GoogleGenerativeAI } = require("@google/generative-ai");
const path = require("path");
const fs = require("fs");
const crypto = require("crypto");
const dotenv = require("dotenv");


dotenv.config();

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });


// Generates an 8-character alphanumeric ID
exports.generateRandomId = () => {
  return crypto.randomBytes(4).toString("hex").toUpperCase();
};

// Generates a 10-digit numeric ID
exports.generateVideoRandomId = () => {
  return Array.from({ length: 10 }, (_, i) =>
    i === 0
      ? crypto.randomInt(1, 10) // Ensure the first digit is not zero
      : crypto.randomInt(0, 10)
  ).join("");
};

exports.analyzeAnswerAndGenerateFollowUp = async (question, answer, role, skills = [], previousQA = [], requirements = null) => {
  try {
    const chat = model.startChat({
      history: previousQA.map(qa => [
        { role: "user", parts: [{ text: qa.question }] },
        { role: "model", parts: [{ text: qa.answer || "No response" }] }
      ]).flat(),
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 1024,
      },
    });

    const analysisPrompt = `You are an expert technical interviewer analyzing a candidate's response.

Question: ${question}
Answer: ${answer || "No response provided"}
Role: ${role}
Required Skills: ${Array.isArray(skills) ? skills.join(', ') : skills}

Analyze the response and provide a detailed evaluation in this exact JSON format:
{
  "analysis": {
    "technical_accuracy": "Detailed analysis of technical correctness",
    "communication": "Analysis of communication effectiveness",
    "knowledge_gaps": ["Specific areas where knowledge is lacking"],
    "strengths": ["Areas where the candidate showed strong understanding"],
    "improvement_suggestions": ["Specific ways to improve the answer"]
  },
  "follow_up": {
    "question": "A follow-up question based on the response",
    "reasoning": "Why this follow-up is appropriate",
    "expected_focus": "What specific aspect this follow-up aims to explore",
    "adaptation_context": "How this follows from their previous answer"
  }
}

Guidelines:
1. If the answer is very brief or missing, focus follow-up on basic concepts
2. If answer shows understanding, probe deeper with more complex follow-ups
3. Address specific knowledge gaps identified
4. Maintain relevance to the ${role} role
5. Consider skill level demonstrated in previous responses`;

    const result = await chat.sendMessage(analysisPrompt);
    const response = cleanJSONResponse(result.response.text());

    if (!response.analysis || !response.follow_up?.question) {
      throw new Error('Invalid response format from analysis');
    }

    // NEW: Requirements assessment if available
    let requirementsStatus = null;
    if (requirements && requirements.requirements) {
      try {
        console.log(`🎯 REQUIREMENTS-ENHANCED ANALYSIS: Processing answer with ${requirements.requirements.length} requirements`);

        const {
          assessAnswerAgainstRequirements,
          updateRequirementsSatisfaction,
          checkCompletionStatus
        } = require('./requirementsAssessment');

        // Assess answer against requirements
        const assessments = await assessAnswerAgainstRequirements(
          answer,
          question,
          requirements.requirements,
          previousQA
        );

        // Update requirements satisfaction status
        const updatedRequirements = updateRequirementsSatisfaction(
          requirements.requirements,
          assessments
        );

        // Update the requirements object
        requirements.requirements = updatedRequirements;

        // Check completion status
        const completionStatus = checkCompletionStatus(requirements);

        requirementsStatus = {
          assessedRequirements: assessments.length,
          satisfiedCount: completionStatus.satisfiedCount,
          totalRequired: completionStatus.totalRequired,
          completionPercentage: completionStatus.completionPercentage,
          canAutoComplete: completionStatus.canAutoComplete,
          nextFocusArea: completionStatus.nextFocusArea,
          unsatisfiedRequirements: completionStatus.unsatisfiedRequirements,
          assessments: assessments
        };

        console.log(`📊 REQUIREMENTS STATUS SUMMARY:`, {
          satisfied: `${completionStatus.satisfiedCount}/${completionStatus.totalRequired}`,
          progress: `${completionStatus.completionPercentage}%`,
          canComplete: completionStatus.canAutoComplete,
          nextFocus: completionStatus.nextFocusArea
        });

      } catch (reqError) {
        console.error("❌ Requirements assessment failed, continuing with standard analysis:", reqError);
        // Continue with standard analysis if requirements assessment fails
      }
    }

    // Return enhanced response with requirements status
    return {
      ...response,
      requirementsStatus: requirementsStatus,
      updatedRequirements: requirements // Include updated requirements for database persistence
    };
  } catch (error) {
    console.error("Error analyzing answer:", error);
    return {
      analysis: {
        technical_accuracy: "Unable to analyze technical content",
        communication: "Unable to analyze communication",
        knowledge_gaps: ["Analysis unavailable"],
        strengths: [],
        improvement_suggestions: ["Please provide more detailed responses"]
      },
      follow_up: {
        question: `Could you elaborate more on your experience with ${Array.isArray(skills) ? skills[0] : 'this topic'}?`,
        reasoning: "Default follow-up due to analysis error",
        expected_focus: "General elaboration",
        adaptation_context: "Continuing from previous response"
      }
    };
  }
};

/**
 * Generate a single interview question, with optional forced type ("behavioral" or "technical").
 * If forcedType is provided, the prompt will instruct the AI to generate only that type.
 * For "technical", skills will always be used.
 */
exports.generateSingleQuestion = async (
  role,
  previousQA,
  skills = [],
  isFirstQuestion = false,
  companyType = '',
  experience = '',
  forcedType = null, // "behavioral" or "technical" or null
  targetRequirement = null // Specific requirement to target for question generation
) => {
  try {
    if (!role) {
      throw new Error("Role is required for question generation");
    }

    const skillsList = (Array.isArray(skills) ? skills : [skills])
      .filter(skill => skill && typeof skill === 'string')
      .map(skill => skill.trim())
      .filter(skill => skill.length > 0);

    const previousQuestionsSet = new Set(
      previousQA.map(qa => (qa.question || '').trim().toLowerCase())
    );

    const history = previousQA.map(qa => [
      {
        role: "user",
        parts: [{ text: qa.question }]
      },
      {
        role: "model",
        parts: [{ text: qa.answer === null || qa.answer === undefined ? "No response" : qa.answer }]
      }
    ]).flat();

    // Enhanced helper to check uniqueness - checks for similar questions, not just exact matches
    function isUniqueQuestion(q) {
      if (!q) return false;

      const normalizedQ = q.trim().toLowerCase();

      // Check exact match
      if (previousQuestionsSet.has(normalizedQ)) {
        return false;
      }

      // Check for similar questions (basic similarity check)
      for (const prevQ of previousQuestionsSet) {
        // Remove common words and check similarity
        const cleanQ = normalizedQ.replace(/\b(the|a|an|and|or|but|in|on|at|to|for|of|with|by|from|about|what|how|why|when|where|can|could|would|should|do|does|did|is|are|was|were|have|has|had|will|would|tell|me|you|your|please|describe|explain)\b/g, '').replace(/\s+/g, ' ').trim();
        const cleanPrevQ = prevQ.replace(/\b(the|a|an|and|or|but|in|on|at|to|for|of|with|by|from|about|what|how|why|when|where|can|could|would|should|do|does|did|is|are|was|were|have|has|had|will|would|tell|me|you|your|please|describe|explain)\b/g, '').replace(/\s+/g, ' ').trim();

        // If cleaned questions are very similar (>70% overlap), consider it not unique
        if (cleanQ.length > 10 && cleanPrevQ.length > 10) {
          const similarity = calculateSimilarity(cleanQ, cleanPrevQ);
          if (similarity > 0.7) {
            return false;
          }
        }
      }

      return true;
    }

    // Simple similarity calculation
    function calculateSimilarity(str1, str2) {
      const words1 = str1.split(' ').filter(w => w.length > 2);
      const words2 = str2.split(' ').filter(w => w.length > 2);

      if (words1.length === 0 || words2.length === 0) return 0;

      const commonWords = words1.filter(word => words2.includes(word));
      return commonWords.length / Math.max(words1.length, words2.length);
    }
    
    let lastError = null;
    let generated = null;
    let attempts = 0;
    const MAX_ATTEMPTS = 3;

    // Map forcedType to AI prompt type
    let aiType = null;
    if (forcedType === "technical") aiType = "coding";
    if (forcedType === "behavioral") aiType = "verbal";

    while (attempts < MAX_ATTEMPTS) {
      attempts++;

      const chat = model.startChat({
        history,
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        },
      });

      // Phase 2: Compose prompt with requirement context for content guidance (not assessment)
      const requirementContextPrompt = targetRequirement ? `

  🎯 REQUIREMENT CONTEXT FOR QUESTION CONTENT:
  This question should address the following requirement area:
  - Requirement Focus: ${targetRequirement.parameter || targetRequirement.description}
  - Category: ${targetRequirement.category}
  - Priority: ${targetRequirement.priority}

  QUESTION UNIQUENESS REQUIREMENT:
  You MUST generate a completely unique question that has never been asked before.
  Previous questions asked: ${previousQA.map(qa => qa.question).join(' | ')}

  CONTENT GUIDANCE:
  Generate a question that naturally covers the requirement area without explicitly mentioning "requirements" or "assessment".
  The question should feel like a natural part of the interview conversation while addressing the specified focus area.
  Focus on practical scenarios, real-world applications, or experience-based questions related to this requirement.

  IMPORTANT:
  - Create a question that allows the candidate to demonstrate knowledge/skills in the requirement area
  - Make it conversational and engaging, not like a formal assessment
  - Ensure the question is completely different from any previously asked questions
  - The question should feel natural and flow well in an interview context
  ` : '';

      const prompt = `You are an expert technical interviewer conducting a one-on-one interview for the "${role}" role.

  Context:
  - Previous Q&A History:
  ${previousQA.map((qa, idx) => {
    const answer = (qa.answer === null || qa.answer === undefined) ? 'No response provided' : qa.answer;
    return `Q${idx + 1} (${qa.type || 'unknown'}): ${qa.question}
  A${idx + 1}: ${answer}`;
  }).join('\n\n')}

  - Provided Skills: ${skillsList.length > 0 ? skillsList.join(', ') : 'None specified, use general knowledge for the role.'}
  - Company Type Preference: ${companyType || 'Not specified'}
  - Experience Level: ${experience || 'Not specified'} years${requirementContextPrompt}

  ${!targetRequirement && previousQA.length > 0 ? `
  🔄 FOLLOW-UP QUESTION GENERATION:
  Since no specific requirement context is provided, generate a thoughtful follow-up question based on the candidate's previous responses.
  Look for areas that could be explored deeper, or ask about related topics that would provide additional insights.
  Build naturally on what the candidate has already shared.
  ` : ''}

  Instructions:
  - Treat this as a real one-on-one interview. For each new question, briefly acknowledge the candidate's previous answer(s) with a few words of encouragement or a simple transition.
  - Keep questions concise, 1-2 lines maximum, like a real interviewer would ask.
  - Ensure a smooth, natural flow between questions without overly detailed explanations of the transition.
  - **Strongly prioritize technical and coding questions.** Ensure a dominant focus on programming logic, syntax, problem-solving through code, and in-depth technical concepts.
  - **Maintain a balance between coding and verbal questions.** While prioritizing coding, ensure verbal questions are still included for conceptual understanding and behavioral aspects.
  - Coding questions are strictly designed to be answered in a code editor, focusing on programming logic, syntax, and problem-solving through code. Return coding for this type.
  - Verbal questions are meant to be answered verbally (e.g., via voice recording) and can encompass both technical concepts (explained verbally) and behavioral scenarios; it should strictly be something that can be answered verbally. Return verbal for this type.
  - **Aim to complete the interview within 6-8 questions.** Design questions to be comprehensive and cover key areas efficiently.
  - Assess the candidate's strengths, weaknesses, and skill level based on the Q&A history.
  - If this is the first question, a verbal question is acceptable to ease the candidate in. For subsequent questions, introduce coding questions early and consistently to ensure a good balance.
  - Incorporate provided skills if relevant, but do not be limited by them.
  - You are generating the ${isFirstQuestion ? 'first' : 'next'} question in the interview.
  ${aiType ? `- The next question MUST be of type "${aiType}". Only generate a question of this type. Do not generate any other type. If "coding", it must be technical and use the provided skills. If "verbal", it must be behavioral or conceptual.` : ''}

  Return ONLY a valid JSON object in this format (no extra text). The 'type' field is mandatory and must be either 'coding' or 'verbal'. The 'timerDuration' field is mandatory and must be between 30-90 seconds based on question complexity. Do not omit any fields.
  {
    "question": "The generated interview question",
    "type": "verbal", // Must be 'coding' or 'verbal'
    "timerDuration": 75 // Must be 30-90 seconds based on question complexity and expected answer depth
  }

  Timer Duration Guidelines:
  - Simple behavioral questions: 30-45 seconds
  - Complex behavioral questions: 45-60 seconds
  - Basic technical questions: 45-60 seconds
  - Complex coding questions: 60-90 seconds
  - Consider the depth of answer expected when determining duration`;

      console.log("Generating question with prompt:", prompt);
      try {
        const result = await chat.sendMessage(prompt);
        const text = result.response.text();

        const jsonMatch = text.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          throw new Error('No JSON object found in response');
        }

        let jsonText = jsonMatch[0];
        jsonText = jsonText
          .replace(/```(?:json)?\s*|\s*```/g, '')
          .replace(/\n/g, ' ')
          .replace(/,\s*}/g, '}')
          .replace(/,\s*]/g, ']');

        let parsed = JSON.parse(jsonText);

        const question = {
          question: String(parsed.question || '').trim(),
          type: String(parsed.type || 'coding').toLowerCase(),
          timerDuration: Math.min(90, Math.max(30, parseInt(parsed.timerDuration) || 90))
        };

        if (!question.question) {
          throw new Error('Generated question text is empty');
        }
        if (isUniqueQuestion(question.question)) {
          console.log("Generated Unique Question Output:", question);
          return question;
        } else {
          lastError = 'Duplicate question generated, retrying...';
          continue;
        }
      } catch (error) {
        lastError = error.message;
        console.error("Question generation error:", error);
        // Try again if not max attempts
      }
    }

    // Remove fallback: throw error if unable to generate a unique question
    throw new Error(lastError || "Failed to generate unique question");
  } catch (error) {
    console.error("Critical error in generation:", error);
    throw new Error('Critical error: ' + error.message);
  }
};

exports.generateScores = async (questions) => {
  if (!questions || !Array.isArray(questions) || questions.length === 0) {
    throw new Error("Invalid interview data provided for scoring.");
  }

  if (!process.env.GOOGLE_API_KEY) {
    throw new Error("Google API key not found. Cannot generate scores.");
  }

  const cleanedInterviewData = questions.map(q => ({
    question: String(q.question || '').trim(),
    answer: q.answer,
    type: q.type, // Assuming questions will have a type now
  }));

  const prompt = `You are an expert AI interview evaluator. Evaluate the provided interview responses in detail, providing overall scores and question-wise analysis.
    
    Return EXACTLY this JSON structure with no other text or formatting, but dont retain the content, it's only for example:
    {
      "score": {
        "Skill_Score": (between 0-10),
        "Communication_Score": (between 0-10),
        "Overall_Score": (between 0-10)
      },
      "evaluation": [
        {
          "Question": "What is your experience with [skill]?",
          "Your_Answer": "Candidate's actual response",
          "Expected_Answer": "Key points that should have been covered",
          "Analysis": {
            "Technical_Understanding": "Analysis of technical knowledge",
            "Communication_Quality": "Analysis of how well the answer was explained",
            "Improvement_Areas": ["Specific areas to improve"],
            "Strengths": ["Strong points in the answer"]
          }
        }
      ],
      "Suggestions": "Detailed constructive feedback",
      "Next_Focus_Areas": ["Areas that need more exploration"]
    }

    Responses to evaluate:
    ${cleanedInterviewData.map((q, idx) =>
      `Question ${idx + 1}: ${q.question}
    Type: ${q.type}
    Expected Keywords: ${(q.keywords && Array.isArray(q.keywords) ? q.keywords.join(", ") : 'none')}
    Answer: ${typeof q.answer === 'string' ? q.answer.trim() :
            q.answer === null || q.answer === undefined || q.answer === "null" ? "Not provided" :
            String(q.answer)}`
    ).join("\n\n")}
    
    Rules:
    - Return only pure JSON.
    - Scores (Skill_Score, Communication_Score, Overall_Score) must be numbers between 0-10.
    - For each question, provide "Expected_Answer", "Technical_Understanding", "Communication_Quality", "Improvement_Areas", and "Strengths".
    - If an answer is null, undefined, or "null", display "Not provided".
    - Provide specific, actionable feedback in "Suggestions".
    - "Next_Focus_Areas" should suggest topics for further interview rounds.
    - Do NOT return fallback scores; if AI processing fails, an error should be thrown.`;
 
  try {
    const result = await model.generateContent(prompt);
    const text = result.response.text();
    
    let cleaned = cleanJSONResponse(text);
    const data = typeof cleaned === 'string' ? JSON.parse(cleaned) : cleaned;

    if (!data || typeof data !== 'object' || !data.score || !data.evaluation) {
      throw new Error('Invalid response format from AI. Missing score or evaluation.');
    }

    const scores = {
      score: {
        Skill_Score: Math.min(10, Math.max(0, Number(data.score?.Skill_Score) || 0)),
        Communication_Score: Math.min(10, Math.max(0, Number(data.score?.Communication_Score) || 0)),
        Overall_Score: Math.min(10, Math.max(0, Number(data.score?.Overall_Score) || 0))
      },
      evaluation: Array.isArray(data.evaluation) ? data.evaluation.map((e, idx) => {
        const originalAnswer = cleanedInterviewData[idx]?.answer;
        return {
          Question: String(e.Question || ''),
          Your_Answer: (() => {
            return originalAnswer === null ||
                   originalAnswer === undefined ||
                   originalAnswer === "null" ||
                   (typeof originalAnswer === 'string' && originalAnswer.trim() === "")
              ? null
              : originalAnswer;
          })(),
          Expected_Answer: String(e.Expected_Answer || ''),
          Analysis: {
            Technical_Understanding: String(e.Analysis?.Technical_Understanding || ''),
            Communication_Quality: String(e.Analysis?.Communication_Quality || ''),
            Improvement_Areas: Array.isArray(e.Analysis?.Improvement_Areas)
              ? e.Analysis.Improvement_Areas
              : [],
            Strengths: Array.isArray(e.Analysis?.Strengths)
              ? e.Analysis.Strengths
              : []
          }
        };
      }) : [],
      Suggestions: String(data.Suggestions || "No specific feedback available."),
      Next_Focus_Areas: Array.isArray(data.Next_Focus_Areas) ? data.Next_Focus_Areas : []
    };

    // Normalize scores to valid range (0-10)
    scores.score = Object.entries(scores.score).reduce((acc, [key, value]) => {
      acc[key] = Math.min(10, Math.max(0, value));
      return acc;
    }, {});

    return scores;

  } catch (error) {
    console.error("Error in score generation:", error);
    console.error("Score generation error details:", {
      error: error.message,
      lastAnswer: cleanedInterviewData[cleanedInterviewData.length - 1]?.answer,
      answerTypes: cleanedInterviewData.map(d => typeof d.answer)
    });
    throw error; // Re-throw the error to ensure no fallback scores are returned
  }
};

function isValidScoreObject(obj) {
  return (
    obj &&
    typeof obj === 'object' &&
    obj.score &&
    typeof obj.score === 'object' &&
    typeof obj.score.Skill_Score === 'number' &&
    typeof obj.score.Communication_Score === 'number' &&
    typeof obj.score.Overall_Score === 'number' &&
    typeof obj.Suggestions === 'string'
  );
}

function defaultScoreObject(reason) {
  return {
    score: {
      Skill_Score: 5,
      Communication_Score: 5,
      Overall_Score: 5
    },
    Suggestions: `Note: Using default scoring due to processing limitations. Reason: ${reason}`,
    _default: true
  };
}


exports.resumeStripper = async (pdfText) => {
  try {
    console.log('=== Starting enhanced resume parsing ===');

    const prompt = `Extract professional information from this resume and return it in the following JSON format.
For skills, identify and prioritize the top 5 most relevant skills based on:
1. Frequency of mention in the resume
2. Recency (skills from recent jobs/projects)
3. Technical depth and complexity
4. Industry relevance and demand

{
  "personal_info": {
    "name": "",
    "email": "",
    "phone": "",
    "location": ""
  },
  "education": [
    {
      "degree": "",
      "institution": "",
      "year": ""
    }
  ],
  "experience": [
    {
      "title": "",
      "company": "",
      "duration": "",
      "responsibilities": []
    }
  ],
  "skills": {
    "primary_skills": ["skill1", "skill2", "skill3", "skill4", "skill5"],
    "secondary_skills": ["skill6", "skill7", "skill8"],
    "all_skills": ["skill1", "skill2", "skill3", "skill4", "skill5", "skill6", "skill7", "skill8"],
    "skill_categories": {
      "technical": ["programming languages", "frameworks", "tools"],
      "soft": ["communication", "leadership", "teamwork"],
      "domain": ["industry-specific skills"]
    }
  },
  "certifications": []
}

Important:
- primary_skills should contain exactly the top 5 most relevant skills
- all_skills should contain the complete list of skills for backward compatibility
- Ensure skills are properly categorized and prioritized based on resume content

Resume text: ${pdfText}`;

    if (!model) {
      const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
      model = genAI.getGenerativeModel({
        model: 'gemini-2.0-flash',
        generationConfig: {
          temperature: 0.2,
          topP: 0.8,
          topK: 40,
          maxOutputTokens: 2048
        }
      });
    }

    console.log('=== Sending request to AI model ===');
    console.log(`Input text length: ${pdfText.length} characters`);

    const result = await model.generateContent(prompt);
    const response = result.response;
    
    console.log('=== Received response from AI ===');
    let responseText;
    
    try {
      responseText = response.text();
      console.log('Response type:', typeof responseText);
      
      // Clean and parse the response
      const parsedResponse = cleanJSONResponse(responseText);
      console.log('Successfully parsed response');
      
      // Enhanced default structure for backward compatibility
      const defaultResponse = {
        personal_info: { name: '', email: '', phone: '', location: '' },
        education: [],
        experience: [],
        skills: {
          primary_skills: [],
          secondary_skills: [],
          all_skills: [],
          skill_categories: {
            technical: [],
            soft: [],
            domain: []
          }
        },
        certifications: []
      };

      // Merge with parsed response
      const enhancedResponse = { ...defaultResponse, ...parsedResponse };

      // Ensure backward compatibility - if skills is still an array, convert it
      if (Array.isArray(enhancedResponse.skills)) {
        const skillsArray = enhancedResponse.skills;
        enhancedResponse.skills = {
          primary_skills: skillsArray.slice(0, 5),
          secondary_skills: skillsArray.slice(5),
          all_skills: skillsArray,
          skill_categories: {
            technical: [],
            soft: [],
            domain: []
          }
        };
      }

      // Ensure primary_skills has maximum 5 items
      if (enhancedResponse.skills.primary_skills && enhancedResponse.skills.primary_skills.length > 5) {
        enhancedResponse.skills.primary_skills = enhancedResponse.skills.primary_skills.slice(0, 5);
      }

      console.log('Enhanced skills structure:', {
        primary_skills: enhancedResponse.skills.primary_skills?.length || 0,
        secondary_skills: enhancedResponse.skills.secondary_skills?.length || 0,
        total_skills: enhancedResponse.skills.all_skills?.length || 0
      });

      return enhancedResponse;
      
    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      console.error('Raw response:', responseText);
      throw new Error(`Failed to parse AI response: ${parseError.message}`);
    }

  } catch (error) {
    console.error("\n=== Error in resumeStripper ===");
    console.error('Error details:', error);
    
    if (error.stack) {
      console.error('\nError stack trace:');
      console.error(error.stack);
    }
    
    // Return enhanced default structure on error
    return {
      personal_info: { name: '', email: '', phone: '', location: '' },
      education: [],
      experience: [],
      skills: {
        primary_skills: [],
        secondary_skills: [],
        all_skills: [],
        skill_categories: {
          technical: [],
          soft: [],
          domain: []
        }
      },
      certifications: []
    };
  }
};

exports.jobDescStripper = async (pdfBuffer) => {
  try {
    const prompt = await fs.promises.readFile(
      "utils/files/jobDescPrompt.txt",
      "utf8"
    );
    if (!prompt) throw new Error("Prompt file is empty");

    let extractedText;
    try {
      extractedText = await pdf(pdfBuffer);
    } catch (pdfErr) {
      console.error("PDF parsing error:", pdfErr);
      return {
        error: "Failed to parse PDF. The file may be corrupted or unsupported.",
        details: pdfErr.message || pdfErr.toString(),
      };
    }
    const cleanPdfText = extractedText.text?.trim?.() || "";
    if (!cleanPdfText) {
      return {
        error: "Extracted text from PDF is empty. Please upload a valid job description PDF.",
      };
    }

    let aiResponse;
    try {
      const result = await model.generateContent(`${prompt}\n\n${cleanPdfText}`);
      aiResponse = result.response;
    } catch (aiErr) {
      console.error("AI model error:", aiErr);
      return {
        error: "Failed to process job description with AI.",
        details: aiErr.message || aiErr.toString(),
      };
    }

    let cleaned;
    try {
      cleaned = cleanJSONResponse(typeof aiResponse.text === "function" ? aiResponse.text() : aiResponse);
      if (typeof cleaned === "object") return cleaned;
      return JSON.parse(cleaned);
    } catch (jsonErr) {
      console.error("AI response parsing error:", jsonErr);
      return {
        error: "AI response was not valid JSON.",
        details: jsonErr.message || jsonErr.toString(),
      };
    }
  } catch (error) {
    console.error("Error processing job description:", error);
    return {
      error: "Failed to process job description due to an unexpected error.",
      details: error.message || error.toString(),
    };
  }
};

function cleanJSONResponse(text) {
  try {
    // If text is already an object, return it directly
    if (typeof text === 'object' && text !== null) {
      return text;
    }
    
    // If text is not a string, try to stringify it first
    if (typeof text !== 'string') {
      text = String(text);
    }

    // First try direct parse
    try {
      return JSON.parse(text);
    } catch (e) {
      // Continue to other methods if direct parse fails
    }

    // Extract JSON object from text
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON object found in response');
    }

    // Clean the text
    let cleanText = jsonMatch[0]
      .replace(/```(?:json)?|```/g, '')  // Remove code blocks
      .replace(/[\n\r]/g, ' ')           // Remove newlines
      .replace(/\s+/g, ' ')              // Normalize spaces
      .replace(/,\s*([}\]])/g, '$1')     // Remove trailing commas
      .replace(/,,+/g, ',')              // Remove double commas
      .replace(/([{\[,])/g, '$1 ')       // Add space after { [ ,
      .replace(/([}\]])/g, ' $1')        // Add space before } ]
      .replace(/\s+/g, ' ')              // Normalize spaces again
      .trim();

    // Attempt to fix missing closing braces/brackets
    const openBraces = (cleanText.match(/{/g) || []).length;
    const closeBraces = (cleanText.match(/}/g) || []).length;
    if (openBraces > closeBraces) {
      cleanText += '}'.repeat(openBraces - closeBraces);
    }
    const openBrackets = (cleanText.match(/\[/g) || []).length;
    const closeBrackets = (cleanText.match(/]/g) || []).length;
    if (openBrackets > closeBrackets) {
      cleanText += ']'.repeat(openBrackets - closeBrackets);
    }

    // Try parsing cleaned text
    return JSON.parse(cleanText);

  } catch (error) {
    console.error("JSON cleaning error:", error);
    // Return a default object for any error
    return {
      score: {
        Skill_Score: 5,
        Communication_Score: 5,
        Overall_Score: 5
      },
      evaluation: [],
      Suggestions: `Failed to clean response: ${error.message}. Using default scores.`
    };
  }
}

/**
 * Generate AI-recommended skills based on job role
 * Returns exactly 5 relevant skills for the given job role
 */
exports.generateSkillRecommendations = async (jobRole) => {
  try {
    if (!jobRole || typeof jobRole !== 'string' || jobRole.trim().length === 0) {
      throw new Error('Valid job role is required for skill recommendations');
    }

    const prompt = `You are an expert career advisor and technical recruiter. Given the job role "${jobRole.trim()}", recommend exactly 5 most relevant and in-demand skills that are essential for this position.

Consider:
1. Core technical skills required for this role
2. Industry-standard tools and technologies
3. Most sought-after skills by employers for this position
4. Skills that would make a candidate competitive in the job market

Return ONLY a JSON array of exactly 5 skill names as strings. No explanations, no additional text.

Example format: ["JavaScript", "React", "Node.js", "SQL", "Git"]

Job Role: ${jobRole.trim()}`;

    if (!model) {
      const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
      model = genAI.getGenerativeModel({
        model: 'gemini-2.0-flash',
        generationConfig: {
          temperature: 0.3,
          topP: 0.8,
          topK: 40,
          maxOutputTokens: 200
        }
      });
    }

    console.log(`=== Generating skill recommendations for role: ${jobRole} ===`);

    const result = await model.generateContent(prompt);
    const response = result.response;
    let responseText = response.text();

    console.log('Raw AI response:', responseText);

    // Clean the response to extract JSON
    responseText = responseText.replace(/```json\s*|\s*```/g, '').trim();

    let skills;
    try {
      skills = JSON.parse(responseText);
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', parseError);
      // Fallback: try to extract skills from text
      const skillMatches = responseText.match(/"([^"]+)"/g);
      if (skillMatches && skillMatches.length >= 5) {
        skills = skillMatches.slice(0, 5).map(match => match.replace(/"/g, ''));
      } else {
        throw new Error('Failed to extract skills from AI response');
      }
    }

    // Validate response
    if (!Array.isArray(skills) || skills.length !== 5) {
      throw new Error(`Expected exactly 5 skills, got ${skills?.length || 0}`);
    }

    // Ensure all skills are valid strings
    const validSkills = skills
      .filter(skill => skill && typeof skill === 'string' && skill.trim().length > 0)
      .map(skill => skill.trim())
      .slice(0, 5);

    if (validSkills.length !== 5) {
      throw new Error(`Invalid skills format. Expected 5 valid strings, got ${validSkills.length}`);
    }

    console.log(`=== Successfully generated ${validSkills.length} skill recommendations ===`);
    return validSkills;

  } catch (error) {
    console.error('Error generating skill recommendations:', error);

    // Return fallback skills based on common job role patterns
    const fallbackSkills = getFallbackSkills(jobRole);
    console.log(`Using fallback skills for ${jobRole}:`, fallbackSkills);
    return fallbackSkills;
  }
};

/**
 * Get fallback skills for common job roles when AI fails
 */
function getFallbackSkills(jobRole) {
  const role = jobRole.toLowerCase().trim();

  if (role.includes('frontend') || role.includes('front-end') || role.includes('react') || role.includes('angular') || role.includes('vue')) {
    return ['JavaScript', 'HTML', 'CSS', 'React', 'Git'];
  } else if (role.includes('backend') || role.includes('back-end') || role.includes('server') || role.includes('api')) {
    return ['Node.js', 'JavaScript', 'SQL', 'REST API', 'Git'];
  } else if (role.includes('fullstack') || role.includes('full-stack') || role.includes('full stack')) {
    return ['JavaScript', 'React', 'Node.js', 'SQL', 'Git'];
  } else if (role.includes('data') && (role.includes('scientist') || role.includes('analyst') || role.includes('engineer'))) {
    return ['Python', 'SQL', 'Machine Learning', 'Pandas', 'Git'];
  } else if (role.includes('devops') || role.includes('cloud') || role.includes('infrastructure')) {
    return ['AWS', 'Docker', 'Kubernetes', 'Linux', 'Git'];
  } else if (role.includes('mobile') || role.includes('android') || role.includes('ios')) {
    return ['Java', 'Kotlin', 'Swift', 'React Native', 'Git'];
  } else if (role.includes('qa') || role.includes('test') || role.includes('quality')) {
    return ['Selenium', 'Java', 'Test Automation', 'API Testing', 'Git'];
  } else if (role.includes('ui') || role.includes('ux') || role.includes('design')) {
    return ['Figma', 'Adobe XD', 'Prototyping', 'User Research', 'Wireframing'];
  } else {
    // Generic software development skills
    return ['JavaScript', 'Python', 'SQL', 'Git', 'Problem Solving'];
  }
}

/**
 * Real-time requirement analysis for individual questions
 * Analyzes a candidate's answer against a specific requirement and generates a score
 * This function is completely isolated from the existing scoring system
 *
 * @param {string} question - The interview question
 * @param {string} answer - The candidate's answer
 * @param {Object} requirement - The specific requirement to analyze against
 * @param {string} role - The job role
 * @param {Array} skills - Required skills
 * @param {number} questionNumber - Current question number
 * @returns {Object} Analysis result with score and assessment
 */
exports.analyzeAnswerAgainstRequirement = async (question, answer, requirement, role, skills = [], questionNumber = 1) => {
  try {
    console.log(`🔍 REAL-TIME REQUIREMENT ANALYSIS START - Question ${questionNumber}`);
    console.log(`📋 REQUIREMENT DETAILS:`, {
      id: requirement.id,
      parameter: requirement.parameter || requirement.description,
      category: requirement.category,
      priority: requirement.priority,
      assessmentCriteria: requirement.assessmentCriteria
    });
    console.log(`❓ QUESTION: ${question.substring(0, 100)}...`);
    console.log(`💬 ANSWER LENGTH: ${answer?.length || 0} characters`);

    // Validate inputs
    if (!question || !requirement) {
      throw new Error("Question and requirement are required for analysis");
    }

    const safeAnswer = answer || "No response provided";

    // Create analysis prompt specifically for this requirement
    const analysisPrompt = `You are an expert technical interviewer conducting real-time requirement analysis.

INTERVIEW CONTEXT:
- Role: ${role}
- Required Skills: ${Array.isArray(skills) ? skills.join(', ') : skills}
- Question Number: ${questionNumber}

QUESTION ASKED:
${question}

CANDIDATE'S ANSWER:
${safeAnswer}

SPECIFIC REQUIREMENT TO ANALYZE:
- Requirement ID: ${requirement.id}
- Parameter: ${requirement.parameter || requirement.description}
- Category: ${requirement.category}
- Priority: ${requirement.priority}
- Assessment Criteria: ${requirement.assessmentCriteria ? requirement.assessmentCriteria.join(', ') : 'General assessment'}

ANALYSIS TASK:
Analyze ONLY how well this specific answer addresses the specific requirement listed above.
Do NOT analyze other requirements or provide overall assessment.

SCORING CRITERIA:
- Score 0-4: Response cannot meet this requirement (lacks evidence, understanding, or relevance)
- Score 5-6: Response partially meets this requirement (some evidence but incomplete)
- Score 7-8: Response adequately meets this requirement (good evidence and understanding)
- Score 9-10: Response excellently meets this requirement (strong evidence, deep understanding)

Return EXACTLY this JSON format:
{
  "requirementId": "${requirement.id}",
  "requirementParameter": "${requirement.parameter || requirement.description}",
  "score": 7.5,
  "meetsRequirement": true,
  "evidence": ["specific quote or example from the answer that demonstrates this requirement"],
  "reasoning": "detailed explanation of why this score was given for this specific requirement",
  "gaps": ["what is missing to fully satisfy this requirement (if any)"],
  "analysisTimestamp": "${new Date().toISOString()}"
}

IMPORTANT: Focus ONLY on the specific requirement provided. Do not assess other skills or requirements.`;

    console.log(`🤖 SENDING ANALYSIS REQUEST to AI model...`);

    const result = await model.generateContent(analysisPrompt);
    const response = result.response.text();

    console.log(`📥 RAW AI RESPONSE received (${response.length} characters)`);

    // Clean and parse the response
    let cleaned = response.trim().replace(/```json\n?/g, '').replace(/```\n?/g, '');
    const parsed = JSON.parse(cleaned);

    // Validate and normalize the response
    const analysis = {
      requirementId: parsed.requirementId || requirement.id,
      requirementParameter: parsed.requirementParameter || requirement.parameter || requirement.description,
      score: Math.min(10, Math.max(0, Number(parsed.score) || 0)),
      meetsRequirement: Number(parsed.score) >= 5, // Apply scoring logic: ≥5 = meets requirement
      evidence: Array.isArray(parsed.evidence) ? parsed.evidence : [],
      reasoning: String(parsed.reasoning || 'No reasoning provided'),
      gaps: Array.isArray(parsed.gaps) ? parsed.gaps : [],
      analysisTimestamp: new Date().toISOString(),
      questionNumber: questionNumber
    };

    console.log(`✅ REAL-TIME ANALYSIS COMPLETED:`, {
      requirementId: analysis.requirementId,
      score: analysis.score,
      meetsRequirement: analysis.meetsRequirement,
      evidenceCount: analysis.evidence.length,
      hasGaps: analysis.gaps.length > 0
    });

    console.log(`📊 REQUIREMENT ASSESSMENT RESULT:`, {
      requirement: analysis.requirementParameter,
      status: analysis.meetsRequirement ? "✅ REQUIREMENT MET" : "❌ REQUIREMENT NOT MET",
      score: `${analysis.score}/10`,
      reasoning: analysis.reasoning.substring(0, 100) + "..."
    });

    return analysis;

  } catch (error) {
    console.error(`❌ REAL-TIME REQUIREMENT ANALYSIS ERROR - Question ${questionNumber}:`, error);

    // Return fallback analysis to ensure system continues working
    const fallbackAnalysis = {
      requirementId: requirement.id,
      requirementParameter: requirement.parameter || requirement.description,
      score: 0,
      meetsRequirement: false,
      evidence: [],
      reasoning: `Analysis failed due to error: ${error.message}`,
      gaps: ["Unable to analyze due to technical error"],
      analysisTimestamp: new Date().toISOString(),
      questionNumber: questionNumber,
      error: true
    };

    console.log(`🔄 FALLBACK ANALYSIS RETURNED for requirement ${requirement.id}`);

    return fallbackAnalysis;
  }
};
