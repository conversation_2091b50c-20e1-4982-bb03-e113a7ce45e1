import React, { useState, useRef, useEffect } from "react";
import { Briefcase, ChartBar, Trophy, Users, X } from "lucide-react";
import LogoImage from "assets/img/Visume-logo-icon.png";
import videoRes from "assets/img/videores-illustration.png";
import { useNavigate, useLocation } from "react-router-dom";
import toast from "react-hot-toast";
import Cookies from "js-cookie";
import Loader from "components/Loader";

const CreateAccount = () => {
  const [isLoading, setIsLoading] = useState(false);

  // Get Google profile data from navigation state
  const location = useLocation();

  // Profile picture state
  const [profilePic, setProfilePic] = useState(null);
  const [profilePicPreview, setProfilePicPreview] = useState(null);
  const [profilePicError, setProfilePicError] = useState("");
  const profilePicInputRef = useRef(null);
  const [imageError, setImageError] = useState(false);

  // Prefill form if Google data is present
  useEffect(() => {
    if (location.state) {
      setFormData((prev) => ({
        ...prev,
        fullName: location.state.name || "",
        email: location.state.email || "",
      }));
      if (location.state.picture) {
        setProfilePicPreview(location.state.picture);
      }
    }
  }, [location.state]);

  const jsregcookie = (data) => {
    const allCookies = Cookies.get(); // Get all cookies
    for (const cookieName in allCookies) {
      Cookies.remove(cookieName); // Remove each cookie
    }
    localStorage.clear();
    Cookies.set("empId", data.emp_id, { expires: 7 });
    Cookies.set("employerId", data.employerId, { expires: 7 });
    Cookies.set("jstoken", data.token, { expires: 7 });
    Cookies.set("role", data.role, { expires: 7 });
  };

  // Remove profile picture handler
  const handleRemoveProfilePic = () => {
    setProfilePic(null);
    setProfilePicPreview(null);
    setProfilePicError("");
    if (profilePicInputRef.current) {
      profilePicInputRef.current.value = "";
    }
  };

  // Profile picture handler
  const handleProfilePicChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const validTypes = ["image/jpeg", "image/png", "image/jpg"];
      if (!validTypes.includes(file.type)) {
        setProfilePicError("Only JPG, JPEG, or PNG files are allowed.");
        setProfilePic(null);
        setProfilePicPreview(null);
        return;
      }
      if (file.size > 5 * 1024 * 1024) {
        setProfilePicError("File size must be less than 5MB.");
        setProfilePic(null);
        setProfilePicPreview(null);
        return;
      }
      setProfilePic(file);
      setProfilePicPreview(URL.createObjectURL(file));
      setProfilePicError("");
    }
  };

  const [emailAvailable, setEmailAvailable] = useState(null);
  const [emailCheckLoading, setEmailCheckLoading] = useState(false);
  const [emailCheckMsg, setEmailCheckMsg] = useState("");

  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    password: "",
    mobile: "",
    designation: "",
  });
  useEffect(() => {
    const checkEmail = async () => {
      if (!formData.email || !formData.email.includes("@")) {
        setEmailAvailable(null);
        setEmailCheckMsg("");
        return;
      }
      setEmailCheckLoading(true);
      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/check-employer-email`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ email: formData.email }),
          }
        );
        const data = await response.json();
        if (response.ok && data.available) {
          setEmailAvailable(true);
          setEmailCheckMsg("");
        } else if (response.status === 409) {
          setEmailAvailable(false);
          setEmailCheckMsg(
            data.message || "Email already exists. Please log in."
          );
        } else {
          setEmailAvailable(null);
          setEmailCheckMsg(data.message || "Error checking email.");
        }
      } catch (err) {
        setEmailAvailable(null);
        setEmailCheckMsg("Error checking email.");
      } finally {
        setEmailCheckLoading(false);
      }
    };
    checkEmail();
  }, [formData.email]);

  const [errors, setErrors] = useState({});
  const navigate = useNavigate();
  const [showSendOTPButton, setShowSendOTPButton] = useState(false);
  const [showOTPModal, setShowOTPModal] = useState(false);
  const [otp, setOTP] = useState(["", "", "", ""]);
  const [generatedOTP, setGeneratedOTP] = useState("");
  const [otpVerified, setOtpVerified] = useState(false);
  const [otpError, setOtpError] = useState("");

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setErrors({ ...errors, [name]: "" });
  };

  const handlePhoneNumberChange = (e) => {
    const value = e.target.value.replace(/\D/g, "").slice(0, 10);
    setFormData({ ...formData, mobile: value });
    setShowSendOTPButton(value.length === 10);
    if (value.length !== 10) {
      setOtpVerified(false);
      setOtpError("");
    }
    setErrors({ ...errors, mobile: "" });
  };

  const handleSendOTPClick = (e) => {
    e.preventDefault();
    const newOTP = Math.floor(1000 + Math.random() * 9000).toString();
    setGeneratedOTP(newOTP);
    alert(`Your OTP is: ${newOTP}`); // In a real app, this would be sent via SMS
    setShowOTPModal(true);
    setOTP(["", "", "", ""]);
    setOtpVerified(false);
    setOtpError("");
  };

  const handleOTPChange = (index, value) => {
    const newOTP = [...otp];
    newOTP[index] = value.replace(/\D/g, "").slice(0, 1);
    setOTP(newOTP);

    if (value && index < 3) {
      const nextInput = document.getElementById(`otp-${index + 1}`);
      if (nextInput) nextInput.focus();
    }
  };

  const handleVerifyOTP = () => {
    const enteredOTP = otp.join("");
    if (enteredOTP === generatedOTP) {
      setOtpVerified(true);
      setOtpError("");
      setShowOTPModal(false);
    } else {
      setOtpVerified(false);
      setOtpError("Wrong OTP entered. Please try again.");
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const newErrors = {};

    // Validate required fields
    if (!formData.fullName) newErrors.fullName = "Full name is required";
    if (!formData.email) newErrors.email = "Email is required";
    if (!formData.password) newErrors.password = "Password is required";
    if (!formData.mobile) newErrors.mobile = "Mobile number is required";
    if (!formData.designation)
      newErrors.designation = "Designation is required";

    // Profile picture is optional, but validate if present
    if (profilePic) {
      const validTypes = ["image/jpeg", "image/png", "image/jpg"];
      if (!validTypes.includes(profilePic.type)) {
        newErrors.profilePic = "Only JPG, JPEG, or PNG files are allowed.";
      }
      if (profilePic.size > 5 * 1024 * 1024) {
        newErrors.profilePic = "File size must be less than 5MB.";
      }
    }

    // Validate phone number verification
    if (!otpVerified) newErrors.mobile = "Please verify your phone number";

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
    } else {
      // Form is valid, proceed with submission
      const finalFormData = {
        emp_email: formData.email,
        password: formData.password,
        emp_name: formData.fullName,
        emp_mobile: formData.mobile,
        designation: formData.designation,
        profile_picture: profilePic
          ? profilePic
          : profilePicPreview &&
            typeof profilePicPreview === "string" &&
            profilePicPreview.startsWith("http") &&
            profilePicPreview.trim() !== ""
          ? profilePicPreview
          : null,
      };
      registerEmployer(finalFormData);
    }
  };

  async function registerEmployer(finalFormData) {
    try {
      setIsLoading(true);

      // Use FormData for file upload
      const formPayload = new FormData();
      formPayload.append("emp_email", finalFormData.emp_email);
      formPayload.append("email", finalFormData.emp_email); // For user table uniqueness
      formPayload.append("password", finalFormData.password);
      formPayload.append("emp_name", finalFormData.emp_name);
      formPayload.append("emp_mobile", finalFormData.emp_mobile);
      formPayload.append("designation", finalFormData.designation);
      if (
        finalFormData.profile_picture &&
        typeof finalFormData.profile_picture !== "string"
      ) {
        formPayload.append("profile_picture", finalFormData.profile_picture);
      }
      if (
        finalFormData.profile_picture &&
        typeof finalFormData.profile_picture === "string" &&
        finalFormData.profile_picture.startsWith("http")
      ) {
        formPayload.append(
          "profile_picture_url",
          finalFormData.profile_picture
        );
      }

      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/register-employeer`,
        {
          method: "POST",
          body: formPayload,
        }
      );

      const resdata = await response.json();

      if (response.ok) {
        console.log(resdata.message); // "Employer registered successfully"
        jsregcookie(resdata); // Store the token, role, candId in a cookie
        navigate("/"); // Redirect to the homepage or other routes
        toast.success("Registered Successfully");
      } else if (response.status === 400) {
        console.error(resdata.message); // Handle validation or missing field errors
        toast.error(resdata.message);
      } else if (response.status === 409) {
        console.error(resdata.message); // "Email already exists"
        toast.error(resdata.message);
      } else if (response.status === 500) {
        console.error(resdata.error); // Detailed error message
        toast.error(resdata.message);
      } else {
        console.error("An unexpected error occurred");
        toast.error(resdata.message);
      }
    } catch (error) {
      toast.error(error?.response?.data?.message || "Something went wrong");
      console.error("Network error:", error);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-brand-50 px-2 py-8 font-sans">
      {isLoading && <Loader text={"Creating Account"} />}
      <div className="mx-auto max-w-5xl">
        <div className="mb-8 flex items-center gap-2">
          <div className="flex items-center text-left font-poppins text-[26px] font-extrabold text-brand-700 dark:text-white">
            <img src={LogoImage} alt="Visume logo" className="mb-1 h-8 w-8" />
            <span className="ml-2 text-2xl font-extrabold tracking-tight">
              Visume.ai
            </span>
          </div>
          <div className="ml-auto text-sm text-gray-600">
            Already Registered?{" "}
            <button
              onClick={() => navigate("/candidate/signIn")}
              className="cursor-pointer text-brand-600 underline underline-offset-2 transition hover:text-brand-800"
            >
              Login here
            </button>
          </div>
        </div>

        <div className="grid gap-10 md:grid-cols-[2fr,1fr]">
          <div className="rounded-2xl border border-brand-100 bg-white p-8 shadow-lg">
            <h2 className="mb-2 text-3xl font-extrabold text-brand-700 tracking-tight">
              Create your Employer Account
            </h2>
            <p className="mb-6 text-base text-gray-500">
              Post jobs and manage applications on India's No.1 AI Video Resume Platform for Employers
            </p>
            <hr className="mb-6 border-t border-brand-100" />
            <form
              className="space-y-6"
              onSubmit={handleSubmit}
              autoComplete="off"
            >
              {/* Profile Picture Upload */}
              <div>
                <label className="mb-2 block text-sm font-semibold text-gray-700">
                  Profile Picture{" "}
                  <span className="text-gray-400">(optional)</span>
                </label>
                <input
                  type="file"
                  accept="image/jpeg, image/png, image/jpg"
                  className="hidden"
                  id="profile-pic-upload-input"
                  onChange={handleProfilePicChange}
                  ref={profilePicInputRef}
                />
                <div
                  className="cursor-pointer rounded-xl border-2 border-dashed p-4 text-center transition hover:border-brand-400 bg-gray-50"
                  onClick={() =>
                    document.getElementById("profile-pic-upload-input").click()
                  }
                >
                  {profilePicPreview && !imageError ? (
                    <div className="flex flex-col items-center">
                      <div className="relative">
                        <img
                          src={
                            typeof profilePicPreview === "string"
                              ? profilePicPreview
                              : URL.createObjectURL(profilePicPreview)
                          }
                          alt="Profile Preview"
                          className="mx-auto mb-2 h-24 w-24 rounded-full border-2 border-brand-300 object-cover shadow-lg"
                          onError={() => setImageError(true)}
                        />
                        <button
                          type="button"
                          aria-label="Remove profile picture"
                          className="absolute right-0 top-0 rounded-full border bg-white p-1 shadow transition hover:bg-brand-50"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveProfilePic();
                            setImageError(false);
                          }}
                        >
                          <X className="h-5 w-5 text-gray-500" />
                        </button>
                      </div>
                      <span className="mt-1 text-xs text-gray-500">
                        {profilePic?.name}
                      </span>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center">
                      <div className="relative">
                        {/* Inline SVG shadow avatar as default */}
                        <svg
                          viewBox="0 0 96 96"
                          width="96"
                          height="96"
                          className="mx-auto mb-2 h-24 w-24 rounded-full border-2 border-brand-100 object-cover"
                          aria-label="Default Profile"
                        >
                          <circle cx="48" cy="48" r="48" fill="#e5e7eb" />
                          <ellipse
                            cx="48"
                            cy="40"
                            rx="20"
                            ry="20"
                            fill="#cbd5e1"
                          />
                          <ellipse
                            cx="48"
                            cy="78"
                            rx="28"
                            ry="14"
                            fill="#d1d5db"
                          />
                        </svg>
                      </div>
                      <p className="mt-1 text-xs text-gray-500">
                        Upload a profile picture (JPG, JPEG, PNG, Max 5MB)
                      </p>
                    </div>
                  )}
                </div>
                {(profilePicError || errors.profilePic) && (
                  <p className="mt-2 text-xs text-red-500">
                    {profilePicError || errors.profilePic}
                  </p>
                )}
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Full name<span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="fullName"
                  placeholder="What is your name?"
                  className="w-full rounded-lg border-2 border-brand-100 p-3 text-gray-700 transition focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-brand-500 shadow-sm"
                  value={formData.fullName}
                  onChange={handleInputChange}
                  required
                />
                {errors.fullName && (
                  <p className="mt-1 text-sm text-red-500">{errors.fullName}</p>
                )}
              </div>

              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Email ID<span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  name="email"
                  placeholder="Tell us your Email ID"
                  className={`w-full rounded-lg border-2 p-3 text-gray-700 transition focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-brand-500 shadow-sm ${
                    emailCheckLoading
                      ? "border-blue-300 focus:ring-blue-500"
                      : emailAvailable === null
                      ? "border-brand-100 focus:ring-brand-500"
                      : emailAvailable
                      ? "border-green-500 focus:ring-green-500"
                      : "border-red-500 focus:ring-red-500"
                  }`}
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-500">{errors.email}</p>
                )}
                {emailCheckLoading && (
                  <p className="mt-1 text-xs text-gray-400">Checking email...</p>
                )}
                {!emailCheckLoading && emailAvailable === false && (
                  <p className="mt-1 text-xs text-red-500">{emailCheckMsg}</p>
                )}
                {!emailCheckLoading && emailAvailable === true && (
                  <p className="mt-1 text-xs text-green-600">Email is available</p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  You'll receive job applications and notifications at this
                  email
                </p>
              </div>

              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Password<span className="text-red-500">*</span>
                </label>
                <input
                  type="password"
                  name="password"
                  placeholder="Minimum 6 characters"
                  className="w-full rounded-lg border-2 border-brand-100 p-3 text-gray-700 transition focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-brand-500 shadow-sm"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  minLength={6}
                />
                {errors.password && (
                  <p className="mt-1 text-sm text-red-500">{errors.password}</p>
                )}
              </div>

              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Mobile number<span className="text-red-500">*</span>
                </label>
                <div className="flex">
                  <span className="inline-flex items-center rounded-l-md border border-r-0 bg-gray-50 px-3 text-gray-500">
                    +91
                  </span>
                  <input
                    type="tel"
                    name="mobile"
                    maxLength="10"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    placeholder="Enter your mobile number"
                    className="w-full rounded-r-lg border-2 border-brand-100 p-3 text-gray-700 transition focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-brand-500 shadow-sm"
                    value={formData.mobile}
                    onChange={handlePhoneNumberChange}
                    required
                  />
                </div>
                {errors.mobile && (
                  <p className="mt-1 text-sm text-red-500">{errors.mobile}</p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  You'll receive job applications and notifications at this
                  number
                </p>

                {showSendOTPButton && !otpVerified && (
                  <button
                    onClick={handleSendOTPClick}
                    className="mt-2 rounded-lg bg-brand-600 px-4 py-2 text-white font-semibold shadow transition hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-brand-500"
                  >
                    Send OTP
                  </button>
                )}
                {otpVerified && (
                  <p className="mt-2 text-green-600">Phone number verified!</p>
                )}
              </div>

              <div>
                <label className="mb-1 block text-sm font-medium text-gray-700">
                  Designation<span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="designation"
                  placeholder="Hiring Manager"
                  className="w-full rounded-lg border-2 border-brand-100 p-3 text-gray-700 transition focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-brand-500 shadow-sm"
                  value={formData.designation}
                  onChange={handleInputChange}
                  required
                />
                {errors.designation && (
                  <p className="mt-1 text-sm text-red-500">
                    {errors.designation}
                  </p>
                )}
              </div>
              <div className="mt-4 flex items-center gap-2">
                <input
                  type="checkbox"
                  className="rounded-lg border-2 border-brand-100 text-brand-600 focus:ring-brand-500"
                />
                <span className="text-xs text-gray-500">
                  Send me candidate referrals and job applications via SMS,
                  email, and WhatsApp
                </span>
              </div>

              <div className="mt-4 text-xs text-gray-400">
                By clicking{" "}
                <span className="font-semibold text-brand-600">Register</span>,
                you agree to the{" "}
                <a
                  href="#"
                  className="text-brand-600 underline underline-offset-2 transition hover:text-brand-800"
                >
                  Terms and Conditions
                </a>{" "}
                &{" "}
                <a
                  href="#"
                  className="text-brand-600 underline underline-offset-2 transition hover:text-brand-800"
                >
                  Privacy Policy
                </a>
              </div>

              <button
                type="submit"
                className="mt-6 w-full rounded-xl bg-brand-600 py-3 text-lg font-semibold text-white shadow-lg transition hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-brand-500"
              >
                Create Visume Profile
              </button>
            </form>
          </div>

          <div className="h-max rounded-2xl border border-brand-100 bg-white p-8 shadow-lg">
            <div className="mb-8">
              <div className="mb-4 flex justify-center">
                <img
                  src={videoRes}
                  alt="Employer illustration"
                  className="w-64 rounded-xl shadow"
                />
              </div>
              <h3 className="mb-6 text-center text-xl font-bold text-brand-700">
                Enhance your hiring process with Visume
              </h3>
              <ul className="space-y-5">
                <li className="flex items-center gap-3">
                  <div className="flex items-center justify-center rounded-full bg-blue-100 p-2 shadow">
                    <ChartBar className="h-5 w-5 text-blue-600" />
                  </div>
                  <span className="text-sm text-gray-700">
                    <strong className="font-semibold">50% faster</strong>{" "}
                    candidate selection compared to traditional methods
                  </span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="flex items-center justify-center rounded-full bg-green-100 p-2 shadow">
                    <Users className="h-5 w-5 text-green-600" />
                  </div>
                  <span className="text-sm text-gray-700">
                    Access to{" "}
                    <strong className="font-semibold">
                      500+ qualified candidates
                    </strong>{" "}
                    in your industry
                  </span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="flex items-center justify-center rounded-full bg-purple-100 p-2 shadow">
                    <Trophy className="h-5 w-5 text-purple-600" />
                  </div>
                  <span className="text-sm text-gray-700">
                    Streamlined{" "}
                    <strong className="font-semibold">
                      candidate presentations
                    </strong>{" "}
                    showcasing skills through video
                  </span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="flex items-center justify-center rounded-full bg-orange-100 p-2 shadow">
                    <Briefcase className="h-5 w-5 text-orange-600" />
                  </div>
                  <span className="text-sm text-gray-700">
                    Access to{" "}
                    <strong className="font-semibold">
                      premium candidate profiles
                    </strong>{" "}
                    tailored for Fortune 500 companies
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {showOTPModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-md">
          <div className="bg-black absolute inset-0 opacity-40"></div>
          <div className="relative z-50 w-full max-w-md rounded-2xl border border-brand-100 bg-white p-8 shadow-2xl">
            <button
              onClick={() => setShowOTPModal(false)}
              className="absolute right-4 top-4 text-gray-400 transition hover:text-brand-600"
            >
              <X className="h-6 w-6" />
            </button>
            <h3 className="mb-6 text-center text-xl font-bold text-brand-700">
              Enter OTP
            </h3>
            <div className="mb-6 flex justify-center gap-3">
              {otp.map((digit, index) => (
                <input
                  key={index}
                  id={`otp-${index}`}
                  type="text"
                  inputMode="numeric"
                  maxLength={1}
                  value={digit}
                  onChange={(e) => handleOTPChange(index, e.target.value)}
                  className="h-12 w-12 rounded-lg border-2 border-brand-100 text-center text-xl font-bold shadow transition focus:border-brand-500 focus:ring-2 focus:ring-brand-500"
                  aria-label={`OTP digit ${index + 1}`}
                />
              ))}
            </div>
            {otpError && (
              <p className="mb-4 text-center text-sm text-red-500">
                {otpError}
              </p>
            )}
            <div className="mt-2 flex justify-between">
              <button
                onClick={handleVerifyOTP}
                className="rounded-lg bg-brand-600 px-5 py-2 font-semibold text-white shadow transition hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2"
              >
                Verify OTP
              </button>
              <button
                onClick={handleSendOTPClick}
                className="text-brand-600 underline underline-offset-2 transition hover:text-brand-800 focus:outline-none"
              >
                Resend OTP
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CreateAccount;
