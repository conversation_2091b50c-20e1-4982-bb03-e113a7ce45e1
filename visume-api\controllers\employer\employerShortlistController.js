// Employer Shortlist and Unlock Operations

const prisma = require("../../config/prisma");

// Fetch shortlisted profiles by employer ID
exports.getShortlistProfiles = async (req, res) => {
  const { emp_id } = req.params;

  if (!emp_id) {
    return res.status(400).json({ message: "Employer ID is required." });
  }

  try {
    // Get all employerprofiles for this employer
    const employerProfiles = await prisma.employerprofiles.findMany({
      where: { emp_id: Number(emp_id) },
      select: { video_resume_id: true, status: true }
    });

    if (!employerProfiles || employerProfiles.length === 0) {
      return res.status(404).json({
        message: "No shortlisted profiles found for this employer.",
      });
    }

    const statusMap = employerProfiles.reduce((acc, profile) => {
      acc[profile.video_resume_id] = profile.status;
      return acc;
    }, {});

    const videoProfileIdsArray = employerProfiles.map(
      (c) => c.video_resume_id
    );

    // Get candidate profiles with jobseeker data
    const candidateProfiles = await prisma.videoprofile.findMany({
      where: { id: { in: videoProfileIdsArray } },
      include: { jobseeker: true }
    });

    if (!candidateProfiles || candidateProfiles.length === 0) {
      return res.status(404).json({
        message: "No candidate profiles found.",
      });
    }

    // Add status from employerprofiles
    const result = candidateProfiles.map((profile) => ({
      ...profile,
      status: statusMap[profile.id] || profile.status,
    }));

    // Convert BigInt values before sending to avoid JSON serialization error
    function convertBigInt(obj) {
      if (typeof obj === 'bigint') return obj.toString();
      if (Array.isArray(obj)) return obj.map(convertBigInt);
      if (obj && typeof obj === 'object') {
        return Object.fromEntries(
          Object.entries(obj).map(([k, v]) => [k, convertBigInt(v)])
        );
      }
      return obj;
    }
    // Ensure all nested BigInt values are converted (including inside result array)
    function convertBigIntDeep(obj) {
      if (typeof obj === 'bigint') return obj.toString();
      if (Array.isArray(obj)) return obj.map(convertBigIntDeep);
      if (obj && typeof obj === 'object') {
        return Object.fromEntries(
          Object.entries(obj).map(([k, v]) => [k, convertBigIntDeep(v)])
        );
      }
      return obj;
    }
    res.status(200).json(
      convertBigIntDeep({
        message: "Candidate profiles with jobseeker data fetched successfully.",
        data: result,
      })
    );
  } catch (error) {
    console.error("Error fetching profiles:", error);
    return res.status(500).json({ message: "Failed to fetch profiles." });
  }
};

// Shortlist a video profile for an employer
exports.shortlistVideoProfile = async (req, res) => {
  const { emp_id, cand_id } = req.body;

  if (!emp_id || !cand_id) {
    return res
      .status(400)
      .json({ message: "Employer ID and Candidate ID are required." });
  }

  try {
    // Find candidate's video profile
    const candidate = await prisma.videoprofile.findFirst({
      where: { video_profile_id: cand_id }
    });

    if (!candidate) {
      return res.status(404).json({ message: "Invalid Video Profile ID." });
    }
    const videoProfileId = candidate.id;

    // Check employer exists
    const employer = await prisma.employer.findUnique({
      where: { id: Number(emp_id) }
    });

    if (!employer) {
      return res.status(404).json({ message: "Invalid Employer ID." });
    }

    // Upsert employerprofiles
    // Use findFirst + update or create since there is no unique constraint on (emp_id, video_resume_id)
    let profile = await prisma.employerprofiles.findFirst({
      where: {
        emp_id: Number(emp_id),
        video_resume_id: videoProfileId
      }
    });

    if (profile) {
      profile = await prisma.employerprofiles.update({
        where: { id: profile.id },
        data: {
          status: "shortlisted",
          shortlisted_at: new Date(),
          unlocked_at: null
        }
      });
    } else {
      profile = await prisma.employerprofiles.create({
        data: {
          emp_id: Number(emp_id),
          video_resume_id: videoProfileId,
          status: "shortlisted",
          shortlisted_at: new Date(),
          unlocked_at: null
        }
      });
    }

    return res.status(200).json({
      message: "Video profile shortlisted successfully.",
      data: {
        id: profile.id,
        video_resume_id: videoProfileId,
        cand_id,
        status: profile.status,
        shortlisted_at: profile.shortlisted_at,
        unlocked_at: profile.unlocked_at,
      },
    });
  } catch (error) {
    console.error("Error shortlisting video profile:", error);
    return res
      .status(500)
      .json({ message: "Failed to shortlist video profile." });
  }
};

// UnShortlist a video profile for an employer
exports.unShortlistVideoProfile = async (req, res) => {
  const { emp_id, cand_id } = req.body;

  if (!emp_id || !cand_id) {
    return res
      .status(400)
      .json({ message: "Employer ID and Candidate ID are required." });
  }

  try {
    // Check employer exists
    const employer = await prisma.employer.findUnique({
      where: { id: Number(emp_id) }
    });

    if (!employer) {
      return res.status(404).json({ message: "Invalid Employer ID." });
    }

    // Find video profile
    const videoProfile = await prisma.videoprofile.findFirst({
      where: { video_profile_id: cand_id }
    });

    if (!videoProfile) {
      return res.status(404).json({ message: "Invalid Video Resume ID." });
    }

    // Check if shortlisted
    const shortlistedCandidate = await prisma.employerprofiles.findFirst({
      where: {
        video_resume_id: videoProfile.id,
        emp_id: Number(emp_id)
      }
    });

    if (!shortlistedCandidate) {
      return res
        .status(404)
        .json({ message: "Candidate is not shortlisted by this employer." });
    }

    // Delete shortlist
    await prisma.employerprofiles.deleteMany({
      where: {
        video_resume_id: videoProfile.id,
        emp_id: Number(emp_id)
      }
    });

    return res.status(200).json({
      message: "Video profile unshortlisted successfully.",
    });
  } catch (error) {
    console.error("Error unshortlisting video profile:", error);
    return res
      .status(500)
      .json({ message: "Failed to unshortlist video profile." });
  }
};

// Unlock a video profile for an employer
exports.unlockVideoProfile = async (req, res) => {
  const { emp_id, video_profile_id } = req.body;

  if (!emp_id || !video_profile_id) {
    return res
      .status(400)
      .json({ message: "Employer ID and Video Profile ID are required." });
  }

  try {
    // Get employer plan
    const plan = await prisma.employerplans.findFirst({
      where: { emp_id: Number(emp_id) }
    });

    if (!plan) {
      return res.status(404).json({ message: "Employer profile not found." });
    }

    if (plan.creditsLeft <= 0) {
      return res.status(429).json({
        message:
          "Please recharge to unlock this video profile. Your credits are over.",
      });
    }

    // Update employerprofiles status if currently shortlisted
    const updated = await prisma.employerprofiles.updateMany({
      where: {
        video_resume_id: video_profile_id,
        emp_id: Number(emp_id),
        status: "shortlisted"
      },
      data: {
        status: "unlocked",
        unlocked_at: new Date()
      }
    });

    if (updated.count === 0) {
      return res.status(404).json({
        message: "Profile not found or not in the shortlisted status.",
      });
    }

    // Deduct credit
    await prisma.employerplans.updateMany({
      where: { emp_id: Number(emp_id) },
      data: { creditsLeft: { decrement: 1 } }
    });

    // Get updated profile
    const updatedProfile = await prisma.employerprofiles.findFirst({
      where: {
        video_resume_id: video_profile_id,
        emp_id: Number(emp_id)
      }
    });

    res.status(200).json({
      message: "Profile updated to unlocked successfully.",
      data: updatedProfile,
    });
  } catch (error) {
    console.error("Error processing video profile update:", error);
    res.status(500).json({ message: "Failed to process profile update." });
  }
};

// Remove a video profile for an employer from shortlisted
exports.removeVideoProfile = async (req, res) => {
  const { emp_id, cand_id } = req.body;

  if (!emp_id || !cand_id) {
    return res
      .status(400)
      .json({ message: "Employer ID and Candidate ID are required." });
  }

  try {
    // Find employer profile by candidate and employer
    const profile = await prisma.employerprofiles.findFirst({
      where: {
        cand_id: cand_id,
        emp_id: Number(emp_id)
      }
    });

    if (!profile) {
      return res.status(404).json({ message: "Video profile not found." });
    }

    if (profile.status === "unlocked") {
      return res
        .status(400)
        .json({ message: "Cannot delete an unlocked video profile." });
    }

    // Delete profile if not unlocked
    const deleted = await prisma.employerprofiles.deleteMany({
      where: {
        cand_id: cand_id,
        emp_id: Number(emp_id),
        status: { not: "unlocked" }
      }
    });

    if (deleted.count === 0) {
      return res
        .status(404)
        .json({ message: "Video profile not found or already removed." });
    }

    res.status(200).json({
      message: "Video profile removed from shortlist successfully.",
    });
  } catch (error) {
    console.error("Error removing video profile:", error);
    res
      .status(500)
      .json({ message: "Failed to remove video profile from shortlist." });
  }
};