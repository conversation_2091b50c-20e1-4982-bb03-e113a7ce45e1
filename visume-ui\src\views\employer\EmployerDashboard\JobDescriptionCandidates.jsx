import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";

import { useNavigate } from "react-router-dom";

const JobDescriptionCandidates = () => {
  const { state } = useLocation();
  const job = state?.job;
  const [candidates, setCandidates] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchCandidates = async () => {
      setLoading(true);
      try {
        if (!job) return;
        // Fetch candidates matching role and skills
        const candRes = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/filterCandidate?role=${encodeURIComponent(job.role)}`
        );
        const candData = await candRes.json();


        let arr = [];
        if (Array.isArray(candData.candidates)) arr = candData.candidates;
        else if (Array.isArray(candData.candidateProfiles)) arr = candData.candidateProfiles;
        else if (Array.isArray(candData)) arr = candData;
        else arr = [];
        setCandidates(arr);
      } catch (err) {
        setCandidates([]);
      } finally {
        setLoading(false);
      }
    };
    if (job) fetchCandidates();
  }, [job]);

  if (loading) return <div>Loading...</div>;
  if (!job) return <div>Job description not found.</div>;

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold mb-4">Candidates for {job.role}</h2>
      <div className="mb-2 text-sm text-gray-700">Skills: {Array.isArray(job.skills) ? job.skills.join(", ") : job.skills}</div>
      <div className="mb-6 text-sm text-gray-700">Location: {Array.isArray(job.location) ? job.location.join(", ") : job.location}</div>
      {Array.isArray(candidates) && candidates.length === 0 ? (
        <div className="text-center py-8 text-gray-500">No matching candidates found.</div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2">
          {Array.isArray(candidates) &&
            candidates.map((cand) => {
              // Parse candidateDetails
              const details = Array.isArray(cand.candidateDetails) ? cand.candidateDetails[0] : {};
              // Parse salary
              let salary = {};
              try {
                salary = cand.salary ? JSON.parse(cand.salary) : {};
              } catch {}
              return (
                <div key={cand.id || cand._id} className="rounded-lg bg-white shadow p-4 flex items-center">
                  <img
                    src={details?.profile_picture ? `${import.meta.env.VITE_APP_HOST}/${details.profile_picture}` : "https://ui-avatars.com/api/?name=" + (details?.cand_name || "Candidate")}
                    alt={details?.cand_name || "Candidate"}
                    className="w-16 h-16 rounded-full object-cover mr-4 border"
                  />
                  <div>
                    <div className="font-semibold text-lg mb-1">{details?.cand_name || cand.name || cand.fullName || "No Name"}</div>
                    <div className="text-sm text-gray-600 mb-1">Role: {cand.role || "N/A"}</div>
                    <div className="text-sm text-gray-600 mb-1">Skills: {Array.isArray(cand.skills) ? cand.skills.join(", ") : cand.skills}</div>
                    <div className="text-sm text-gray-600 mb-1">Experience: {cand.experience_range || "N/A"}</div>
                    <div className="text-sm text-gray-600 mb-1">Location: {details?.preferred_location || "N/A"}</div>
                    <div className="text-sm text-gray-600 mb-1">Salary: Current {salary.current || "N/A"}, Expected {salary.expected || "N/A"}</div>
                    {cand.video_profile_id && (
                      <button
                        className="inline-block mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                        onClick={() => navigate(`/employer/candidate-profile/${cand.video_profile_id}`)}
                      >
                        View Video Resume
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
        </div>
      )}
    </div>
  );
};

export default JobDescriptionCandidates;