// CandidateRow.jsx
import React from "react";
import { useNavigate } from "react-router-dom";
import avatar4 from "../../../assets/img/avatars/avatar4.png";
import defaultProfile from "../../../assets/img/default-profile.png";
import {
  HiOutlineVideoCamera,
  HiOutlineDocumentText,
  HiOutlineBriefcase,
  HiLockOpen,
  HiLockClosed,
  HiHeart,
} from "react-icons/hi";
import ScoreButton from "../components/ScoreButton";

const CandidateRow = ({
  candidate,
  loadingId,
  onStatusChange,
  onToggleUnlock,
  unShortlistCandidate,
  tabName,
}) => {
  const navigate = useNavigate();
  return (
    <tr className="border-t border-gray-300 dark:border-navy-600">
      <td className="flex items-center p-2">
        <input type="checkbox" className="mr-2" />
        <div className="flex items-center space-x-4">
          <img
            src={
              candidate.profile_picture
                ? `${import.meta.env.VITE_APP_HOST}/${candidate.profile_picture}`
                : candidate.jobseeker && candidate.jobseeker.profile_picture
                ? `${import.meta.env.VITE_APP_HOST}/${candidate.jobseeker.profile_picture}`
                : defaultProfile
            }
            alt={`${
              candidate.cand_name ||
              (candidate.jobseeker && candidate.jobseeker.cand_name) ||
              "Candidate"
            } avatar`}
            className="h-10 w-10 rounded-full"
            onError={(e) => {
              if (e.target.src !== defaultProfile) {
                e.target.onerror = null;
                e.target.src = defaultProfile;
              }
            }}
          />
          <div>
            <h3
              onClick={() =>
                navigate(`/profile/${candidate.video_profile_id}`)
              }
              className="flex cursor-pointer items-center gap-1 font-extrabold text-gray-800 hover:text-brand-500 dark:text-white"
            >
              {candidate.cand_name}
              <HiOutlineVideoCamera />
              <HiOutlineDocumentText />
            </h3>
            <div className="flex items-center space-x-1">
              <HiOutlineBriefcase className="text-gray-500 dark:text-gray-400" />
              <p className="text-gray-500 dark:text-gray-400">
                {candidate.preferred_location}
              </p>
            </div>
          </div>
        </div>
      </td>
      <td className="p-2">
        {candidate.role ? candidate.role : "Full Stack Developer"}
      </td>
      <td className="p-2">
        <div className="flex flex-wrap space-x-2">
          {candidate.cand_skills &&
            candidate.cand_skills.split(",").map((skill, index) => (
              <span
                key={index}
                className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-800"
              >
                {skill.trim()}
              </span>
            ))}
        </div>
      </td>
      <td className="p-2">
        {/* Combined Scores */}
        <ScoreButton
          overallScore={JSON.parse(candidate?.score)?.score?.Overall_Score || 0}
          skillScore={JSON.parse(candidate?.score)?.score?.Skill_Score || 0}
          communicationScore={
            JSON.parse(candidate?.score)?.score?.Communication_Score || 0
          }
        />
      </td>
      <td className="p-2">
        {candidate.unlocked ? candidate.cand_email : "***@***.com"}
      </td>
      <td className="p-2">
        <select
          value={
            candidate.status.charAt(0).toUpperCase() + candidate.status.slice(1)
          }
          onChange={(e) => onStatusChange(candidate.id, e.target.value)}
          className="rounded border border-gray-300 px-2 py-1"
        >
          {["Shortlisted", "Unlocked", "Interviewed", "Offers"]
            .filter((status) => {
              // Conditional filtering based on candidate's current status
              if (candidate.status == "shortlisted") return candidate.status;
              if (candidate.status == "unlocked")
                return status !== "Shortlisted";
              if (candidate.status == "interviewed")
                return status != "Shortlisted" || status !== "unlocked";
              if (candidate.status === "offers") return false;
              return status == "Offers";
            })
            .map((status) => (
              <option key={status} value={status}>
                {loadingId === candidate.video_profile_id
                  ? "Loading..."
                  : status}
              </option>
            ))}
        </select>
      </td>
      <td className="flex w-max items-center justify-center gap-5 p-2">
        <button
          onClick={() =>
            tabName.toLowerCase() != "unlocked" &&
            onStatusChange(candidate.id, "Unlocked")
          }
          className={`flex flex-row items-center gap-1 rounded text-sm font-semibold  transition-colors 
             duration-200 hover:bg-blue-200 ${
               tabName.toLowerCase() != "unlocked"
                 ? "text-blue-600 hover:text-blue-700"
                 : "cursor-not-allowed text-gray-600 hover:text-gray-700"
             }`}
        >
          {tabName.toLowerCase() != "unlocked" ? (
            <HiLockOpen />
          ) : (
            <HiLockClosed />
          )}
          {tabName.toLowerCase() != "unlocked" ? "Unlock" : "Unlocked Already"}
        </button>
        {tabName.toLowerCase() != "unlocked" && (
          <button
            onClick={() => {
              unShortlistCandidate(
                candidate.video_profile_id,
                candidate.cand_id
              );
            }}
            className={`flex flex-row items-center gap-1 rounded text-sm font-semibold text-red-600 transition-colors 
           duration-200 hover:bg-red-200 hover:text-red-700 `}
          >
            <HiHeart className="fill-current text-base" />
            {loadingId == candidate.cand_id ? (
              <svg
                className="text-current h-4 w-4 animate-spin"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                  fill="none"
                />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
            ) : (
              "UnShortlist"
            )}
          </button>
        )}
      </td>
    </tr>
  );
};

export default CandidateRow;