"use client"
import { motion } from "framer-motion"

const Features = () => {
  const features = [
    {
      title: "Create Your Visume in Just 5 Minutes",
      description: "Quick and easy video resume creation with our intuitive platform and guided prompts.",
      image: "/features/create visume.png",
    },
    {
      title: "Reach 1000s of recruiters from just one visume",
      description: "Expand your reach with a single professional video resume across multiple platforms.",
      image: "/features/reach-recruiters.png",
    },
    {
      title: "AI-Powered Feedback to Perfect Your Visume",
      description: "Get comprehensive AI-powered feedback to improve your presentation and communication skills.",
      image: "/features/aifix.png",
    },
  ]

  return (
    <section className="py-10 lg:py-14 bg-white relative" id="features">
      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <motion.div
          className="mb-12 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <motion.div
            className="mb-6 inline-flex items-center rounded-full bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200/50 px-6 py-2.5 text-sm font-medium text-blue-700 shadow-sm"
            style={{ fontFamily: "Sora, sans-serif" }}
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            Features
          </motion.div>
          <h2
            className="text-slate-900 mb-6 text-3xl font-bold leading-tight sm:text-4xl lg:text-5xl"
            style={{ fontFamily: "Manrope, sans-serif" }}
          >
            Everything you need in{" "}
            <span className="text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text">
              one platform
            </span>
          </h2>
        </motion.div>

        <div className="grid gap-6 md:grid-cols-3">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className="group relative"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.2, duration: 0.6 }}
              whileHover={{ y: -8 }}
            >
              <div className="relative h-full rounded-2xl bg-white border-2 border-blue-200/60 p-6 shadow-xl shadow-blue-200/25 transition-all duration-500 group-hover:shadow-2xl group-hover:shadow-blue-300/35 group-hover:border-blue-300/80">
                {/* Title at the top */}
                <div className="text-left mb-6">
                  <h3
                    className="text-slate-900 text-lg font-bold leading-tight transition-colors duration-300 group-hover:text-blue-700"
                    style={{ fontFamily: "Manrope, sans-serif" }}
                  >
                    {feature.title}
                  </h3>
                </div>

                {/* Feature Icon/Image Container in the middle */}
                <div className="relative z-10 mb-6 flex justify-center">
                  <motion.div
                    className="flex h-44 w-44 items-center justify-center"
                    whileHover={{ scale: 1.05 }}
                    transition={{
                      type: "spring",
                      stiffness: 400,
                      damping: 15,
                    }}
                  >
                    <img
                      src={feature.image || "/placeholder.svg"}
                      alt={feature.title}
                      className="h-full w-full object-contain transition-all duration-300 group-hover:scale-110 drop-shadow-lg"
                    />
                  </motion.div>
                </div>

                {/* Description at the bottom */}
                <div className="text-left">
                  <p
                    className="text-slate-600 text-base leading-relaxed text-left"
                    style={{ fontFamily: "Sora, sans-serif" }}
                  >
                    {feature.description}
                  </p>
                </div>

                {/* Subtle Gradient Overlay */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-50/20 via-transparent to-indigo-50/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Enhanced Custom Styles */}
      <style>{`
        @import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&family=Sora:wght@100;200;300;400;500;600;700;800&display=swap');
      `}</style>
    </section>
  )
}

export default Features
