// CandidateProfile.jsx (refactored)
import { ScoreCard } from "./ProfileHeader";
import { Star, MessageCircle, BarChart2 } from "lucide-react";
import React, { useEffect, useState, useRef } from "react";
import { useNavigate, useParams } from "react-router-dom";
import Loader from "components/Loader";
import CustomNavbar from "../components/CustomNavbar";
import {
  HiCollection,
  HiHome,
  HiOutlineSparkles,
  HiSearch,
  HiAcademicCap,
  HiBriefcase,
  HiBadgeCheck,
  HiStar,
  HiOutlineUser,
  HiOutlineGlobeAlt,
  HiOutlineDocumentText,
} from "react-icons/hi";
import { MdBarChart } from "react-icons/md";
import { LuIndianRupee } from "react-icons/lu";
import toast from "react-hot-toast";
import { AlertCircle, CreditCard, Lock, X, Mail, Phone } from "lucide-react";
import ProfileHeader from "./ProfileHeader";
import ProfileDetails from "./ProfileDetails";

const CandidateProfile = () => {
  const links = [
    { text: "Dashboard", url: "/employer/", icon: <HiHome /> },
    {
      text: "Track Candidates",
      url: "/employer/track-candidates",
      icon: <MdBarChart className="h-6 w-6" />,
    },
    {
      text: (
        <span className="flex items-center">
          Source with AI{" "}
          <span className="ml-2 rounded-full bg-orange-400 px-2 text-xs font-semibold text-white">
            Beta
          </span>
        </span>
      ),
      url: "/employer",
      icon: <HiOutlineSparkles />,
      className: "text-orange-400 font-bold hover:text-orange-500",
    },
  ];
  // Tab state for horizontal sections under video
  // Split tab state for left and right columns
  const [leftTab, setLeftTab] = useState("score");
  const [rightTab, setRightTab] = useState("qa");
  const [subPopup, setSubPopup] = useState(false);
  const [detailsBlur, setDetailsBlur] = useState(true);
  const [profileData, setProfileData] = useState(null);
  const [questionsAndAnswers, setQuestionsAndAnswers] = useState([]);
  // Video ref for controlling playback
  const videoRef = useRef(null);

  // Handler to seek and play video at a given timestamp or ISO string
  const handleQuestionClick = (qa) => {
    let seekTime = 0;
    if (
      qa &&
      qa.startTimestamp &&
      Array.isArray(questionsAndAnswers) &&
      questionsAndAnswers.length > 0 &&
      questionsAndAnswers[0].startTimestamp
    ) {
      const base = new Date(questionsAndAnswers[0].startTimestamp);
      const target = new Date(qa.startTimestamp);
      if (!isNaN(base.getTime()) && !isNaN(target.getTime())) {
        seekTime = Math.max(0, (target.getTime() - base.getTime()) / 1000);
      }
    }
    if (videoRef.current) {
      videoRef.current.currentTime = seekTime;
      videoRef.current.play();
    }
  };
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  let { vpid } = useParams();
  const [candidateProf, setCandidateProf] = useState(null);
  const [strippedResumeJson, setStrippedResumeJson] = useState(null);

  // Q&A expand/collapse state for each question
  const [expandedQA, setExpandedQA] = useState({});
  const toggleExpandQA = (idx) => {
    setExpandedQA((prev) => ({
      ...prev,
      [idx]: !prev[idx],
    }));
  };

  // Sidebar open state for mobile/tablet
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Debug: Log sidebar open state at top of render
  console.log("Render CandidateProfile", sidebarOpen);

  // Detect if screen is small (tailwind: < lg)
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  useEffect(() => {
    const checkScreen = () => setIsSmallScreen(window.innerWidth < 1024);
    checkScreen();
    window.addEventListener("resize", checkScreen);
    return () => window.removeEventListener("resize", checkScreen);
  }, []);

  // Prevent background scroll when sidebar is open on small screens
  useEffect(() => {
    if (isSmallScreen && sidebarOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isSmallScreen, sidebarOpen]);
  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/video-resume-data/${vpid}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        const data = await response.json();
        // TEMP LOG: Inspect API response and questionsAndAnswers
        console.log(
          "Full API response from /api/v1/video-resume-data/:vpid:",
          data
        );
        console.log(
          "questionsAndAnswers from API response:",
          data?.questionsAndAnswers
        );
        setProfileData(data.data);
        // Use backend-provided timestamps only
        console.log(
          "DEBUG: questionsAndAnswers from API:",
          data.questionsAndAnswers
        );
        setQuestionsAndAnswers(data.questionsAndAnswers || []);

        const additionalResponse = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/${
            data.data.cand_id
          }`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );
        if (!additionalResponse.ok) {
          throw new Error("Network response for additional data was not ok");
        }
        const additionalData = await additionalResponse.json();
        const candidateProfile = additionalData.candidateProfile[0];
        // Add emp_id from cookies and video_profile_id from vpid
        const emp_id =
          window.Cookies?.get("employerId") ||
          (await import("js-cookie")).default.get("employerId");
        setCandidateProf({
          ...candidateProfile,
          emp_id,
          video_profile_id: vpid || data.data.id,
        });
        // TEMP LOG: Inspect candidateProf structure
        console.log("candidateProf structure:", {
          ...candidateProfile,
          emp_id,
          video_profile_id: vpid || data.data.id,
        });

        const stripped_resume_json = candidateProfile.stripped_resume;
        setStrippedResumeJson(stripped_resume_json);
        // TEMP LOG: Inspect strippedResumeJson structure
        console.log("strippedResumeJson structure:", stripped_resume_json);
        if (!stripped_resume_json) {
          toast.error("Resume Not Uploaded Properly Details are Missing.");
        }
      } catch (error) {
        console.error("Failed to fetch profile data:", error);
        setError("Failed to fetch profile data");
      } finally {
        setLoading(false);
      }
    };

    if (vpid) {
      fetchProfileData();
    }
  }, [vpid]);

  const profiles = [
    {
      name: "John Doe",
      role: "Front-End Developer",
      rating: 4.5,
      image: "https://via.placeholder.com/50",
    },
    {
      name: "Jane Smith",
      role: "UX Designer",
      rating: 4.8,
      image: "https://via.placeholder.com/50",
    },
    {
      name: "Alex Johnson",
      role: "Full-Stack Developer",
      rating: 4.2,
      image: "https://via.placeholder.com/50",
    },
  ];
  const navigate = useNavigate();

  if (loading) return <Loader />;
  if (!profileData || !candidateProf) return null;

  return (
    <div className="min-h-screen bg-gray-100 p-3">
      <CustomNavbar links={links} />
      <div className="mx-auto">
        <div className="min-h-screen bg-gray-100  p-4">
          {/* Back Button */}
          <div className="mx-6 mb-2 max-w-3xl">
            <button
              onClick={() => {
                const previousUrl = localStorage.getItem("previousUrl");
                if (previousUrl) {
                  window.location.href = previousUrl;
                } else {
                  navigate("/profile-search/filterCandidate");
                }
              }}
              className="flex items-center text-sm font-semibold text-brand-600"
            >
              &larr; Back to Candidates
            </button>
          </div>

          {/* Sidebar open button for small screens */}
          {isSmallScreen && (
            <div className="mb-4 flex justify-end">
              <button
                className="rounded-md bg-brand-600 px-4 py-2 text-white shadow hover:bg-brand-700"
                onClick={() => {
                  console.log(
                    "[Sidebar] Hamburger/menu button clicked: opening sidebar"
                  );
                  setSidebarOpen(true);
                }}
                aria-label="Open sidebar"
              >
                Show Similar Profiles
              </button>
            </div>
          )}

          <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:space-y-0">
            {/* Left Column */}
            <div className="space-y-4">
              <ProfileHeader
                candidateProf={candidateProf}
                profileData={profileData}
                strippedResumeJson={strippedResumeJson}
                questionsAndAnswers={questionsAndAnswers}
                videoRef={videoRef}
              />
              {/* Only keep shortlist button under video. Removed duplicate details. */}
              {/* Inline Section Selector - Improved Visuals */}
              <div className="mb-4 flex items-center justify-start gap-0 text-base font-semibold">
                {[
                  { key: "score", label: "Visume Score" },
                  { key: "summary", label: "Professional Summary" },
                  { key: "experience", label: "Experience" },
                  { key: "education", label: "Education" },
                  { key: "similar", label: "Similar Profiles" },
                ].map((tab, idx, arr) => (
                  <React.Fragment key={tab.key}>
                    <span
                      className={`cursor-pointer border-b-2 px-4 py-2 transition ${
                        leftTab === tab.key
                          ? "border-brand-700 font-bold text-brand-700"
                          : "border-transparent text-gray-500 hover:text-brand-600"
                      }`}
                      style={{ minWidth: "120px", textAlign: "center" }}
                      onClick={() => setLeftTab(tab.key)}
                    >
                      {tab.label}
                    </span>
                    {idx < arr.length - 1 && (
                      <span className="mx-0 text-lg font-bold text-gray-300">
                        |
                      </span>
                    )}
                  </React.Fragment>
                ))}
              </div>
              <div className="mt-6 grid gap-6 md:grid-cols-2">
                {candidateProf?.stripped_resume ? (
                  <div className="col-span-2 flex flex-col gap-6">
                    {/* Visume Score */}
                    {leftTab === "score" && (
                      <div>
                        {/* Replace with actual score logic if available */}
                        <div className="rounded-2xl border border-gray-200 bg-white p-6 shadow">
                          <h2 className="mb-2 flex items-center gap-2 text-lg font-semibold">
                            <HiStar className="h-5 w-5 text-yellow-500" />
                            Visume Scores
                          </h2>
                          <div className="flex flex-col gap-4">
                            {(() => {
                              let scoreObj = {};
                              try {
                                scoreObj =
                                  JSON.parse(profileData.score)?.score || {};
                              } catch (e) {}
                              return (
                                <>
                                  <ScoreCard
                                    icon={
                                      <Star className="h-6 w-6 text-yellow-500" />
                                    }
                                    title="Skill"
                                    score={
                                      scoreObj.Skill_Score ??
                                      scoreObj.Skill ??
                                      0
                                    }
                                    color="yellow"
                                  />
                                  <ScoreCard
                                    icon={
                                      <MessageCircle className="h-6 w-6 text-blue-500" />
                                    }
                                    title="Communication"
                                    score={
                                      scoreObj.Communication_Score ??
                                      scoreObj.Communication ??
                                      0
                                    }
                                    color="blue"
                                  />
                                  <ScoreCard
                                    icon={
                                      <BarChart2 className="h-6 w-6 text-green-500" />
                                    }
                                    title="Overall"
                                    score={
                                      scoreObj.Overall_Score ??
                                      scoreObj.Overall ??
                                      0
                                    }
                                    color="green"
                                  />
                                </>
                              );
                            })()}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Professional Summary */}
                    {leftTab === "summary" && (
                      <div>
                        <div className="rounded-2xl border border-gray-200 bg-white p-6 shadow">
                          <h2 className="mb-2 flex items-center gap-2 text-lg font-semibold">
                            <HiOutlineUser className="h-5 w-5 text-brand-600" />
                            Professional Summary
                          </h2>
                          <div className="text-base text-gray-800">
                            {strippedResumeJson?.summary ??
                              candidateProf?.summary ??
                              "No professional summary available."}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Experience */}
                    {leftTab === "experience" && (
                      <div>
                        {strippedResumeJson?.experience &&
                        strippedResumeJson.experience.length > 0 ? (
                          <div className="flex flex-col gap-3 rounded-2xl border border-gray-100 bg-white p-6 shadow">
                            <div className="mb-2 flex items-center gap-2">
                              <HiBriefcase className="h-5 w-5 text-amber-500" />
                              <h2 className="text-lg font-semibold">
                                Experience
                              </h2>
                            </div>
                            <div className="flex flex-col gap-4">
                              {strippedResumeJson.experience.map((exp, idx) => (
                                <div
                                  key={idx}
                                  className="border-b pb-2 last:border-b-0"
                                >
                                  <div className="flex items-center gap-2">
                                    <span className="font-semibold">
                                      {exp.title || exp.position}
                                    </span>
                                    {exp.company && (
                                      <span className="text-gray-600">
                                        @ {exp.company}
                                      </span>
                                    )}
                                    {exp.duration && (
                                      <span className="ml-2 rounded bg-amber-50 px-2 py-0.5 text-xs text-amber-700">
                                        {exp.duration}
                                      </span>
                                    )}
                                  </div>
                                  {exp.description && (
                                    <div className="mt-1 text-sm text-gray-600">
                                      {exp.description}
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        ) : (
                          <div className="rounded-2xl border border-gray-200 bg-white p-6 shadow">
                            <h2 className="mb-2 flex items-center gap-2 text-lg font-semibold">
                              <HiBriefcase className="h-5 w-5 text-amber-500" />
                              Experience
                            </h2>
                            <div className="text-gray-600">
                              No experience details available.
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Education */}
                    {leftTab === "education" && (
                      <div>
                        {strippedResumeJson?.education &&
                        strippedResumeJson.education.length > 0 ? (
                          <div className="flex flex-col gap-3 rounded-2xl border border-gray-100 bg-white p-6 shadow">
                            <div className="mb-2 flex items-center gap-2">
                              <HiAcademicCap className="h-5 w-5 text-green-600" />
                              <h2 className="text-lg font-semibold">
                                Education
                              </h2>
                            </div>
                            <div className="flex flex-col gap-4">
                              {strippedResumeJson.education.map((edu, idx) => (
                                <div
                                  key={idx}
                                  className="border-b pb-2 last:border-b-0"
                                >
                                  <div className="flex items-center gap-2">
                                    <span className="font-semibold">
                                      {edu.degree}
                                    </span>
                                    {edu.institution && (
                                      <span className="text-gray-600">
                                        @ {edu.institution}
                                      </span>
                                    )}
                                    {edu.year && (
                                      <span className="ml-2 rounded bg-green-50 px-2 py-0.5 text-xs text-green-700">
                                        {edu.year}
                                      </span>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        ) : (
                          <div className="rounded-2xl border border-gray-200 bg-white p-6 shadow">
                            <h2 className="mb-2 flex items-center gap-2 text-lg font-semibold">
                              <HiAcademicCap className="h-5 w-5 text-green-600" />
                              Education
                            </h2>
                            <div className="text-gray-600">
                              No education details available.
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Similar Profiles */}
                    {leftTab === "similar" && (
                      <div>
                        <div className="rounded-2xl border border-gray-200 bg-white p-6 shadow">
                          <h2 className="mb-4 flex items-center gap-2 text-lg font-semibold">
                            <HiOutlineSparkles className="h-5 w-5 text-orange-400" />
                            Similar Profiles
                          </h2>
                          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                            {profiles.map((profile, idx) => (
                              <div
                                key={idx}
                                className="flex items-center gap-4 rounded-lg border border-gray-100 bg-gray-50 p-4 shadow"
                              >
                                <img
                                  src={profile.image}
                                  alt={profile.name}
                                  className="h-12 w-12 rounded-full object-cover"
                                />
                                <div>
                                  <div className="font-semibold text-gray-900">
                                    {profile.name}
                                  </div>
                                  <div className="text-sm text-gray-600">
                                    {profile.role}
                                  </div>
                                  <div className="text-xs text-yellow-600">
                                    Rating: {profile.rating}
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="col-span-2 text-red-500">
                    Resume details not available.
                  </div>
                )}
              </div>
            </div>
            {/* Right Column */}
            <div className="flex flex-col space-y-4">
              {/* Section selector for Q&A and Ai Assistance */}
              <div className="mb-4 flex gap-2">
                <button
                  className={`rounded-t-lg border-b-2 px-4 py-2 font-bold ${
                    rightTab === "qa"
                      ? "border-green-700 bg-white text-green-700"
                      : "border-transparent bg-gray-100 text-gray-500"
                  }`}
                  onClick={() => setRightTab("qa")}
                >
                  Interview Questions & Answers
                </button>
                <button
                  className={`rounded-t-lg border-b-2 px-4 py-2 font-bold ${
                    rightTab === "ai"
                      ? "border-blue-700 bg-white text-blue-700"
                      : "border-transparent bg-gray-100 text-gray-500"
                  }`}
                  onClick={() => setRightTab("ai")}
                >
                  Ai Assistance
                </button>
              </div>
              {/* Q&A Section */}
              {rightTab === "qa" &&
                Array.isArray(questionsAndAnswers) &&
                questionsAndAnswers.length > 0 && (
                  <div className="mb-4">
                    {/* Render all Q&A as expandable cards */}
                    <div className="flex flex-col gap-4">
                      {questionsAndAnswers.map((qa, idx) => (
                        <div
                          key={idx}
                          className="rounded-2xl border border-gray-200 bg-gradient-to-br from-white to-gray-50 shadow-lg transition"
                        >
                          <div className="flex items-center justify-between px-6 py-5">
                            <div
                              className="flex cursor-pointer items-center gap-3"
                              onClick={() => handleQuestionClick(qa)}
                              title={
                                qa.startTimestamp
                                  ? `Jump to ${qa.startTimestamp}`
                                  : undefined
                              }
                              style={{ flex: 1 }}
                            >
                              <span className="font-bold text-brand-700">
                                Q{idx + 1}:
                              </span>
                              <span className="text-base font-semibold text-gray-900">
                                {qa.question}
                              </span>
                              {qa.startTimestamp && (
                                <span className="ml-2 rounded bg-green-100 px-2 py-0.5 font-mono text-xs text-green-700">
                                  {(() => {
                                    let sec = 0;
                                    if (typeof qa.startTimestamp === "number") {
                                      sec = qa.startTimestamp;
                                    } else {
                                      const d = new Date(qa.startTimestamp);
                                      if (!isNaN(d.getTime())) {
                                        sec = Math.floor(
                                          (d.getTime() -
                                            new Date(
                                              questionsAndAnswers[0].startTimestamp
                                            ).getTime()) /
                                            1000
                                        );
                                      }
                                    }
                                    const m = String(
                                      Math.floor(sec / 60)
                                    ).padStart(2, "0");
                                    const s = String(sec % 60).padStart(2, "0");
                                    return `${m}:${s}`;
                                  })()}
                                </span>
                              )}
                            </div>
                            <button
                              className="ml-4 rounded bg-brand-600 px-3 py-1 text-xs text-white hover:bg-brand-700"
                              aria-label={
                                expandedQA[idx]
                                  ? "Collapse answer"
                                  : "Expand answer"
                              }
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleExpandQA(idx);
                              }}
                            >
                              {expandedQA[idx] ? "Collapse" : "Expand"}
                            </button>
                          </div>
                          {expandedQA[idx] && (
                            <div className="px-6 pb-5">
                              <div className="flex items-start gap-3">
                                <span className="pt-1 text-lg font-bold text-green-700">
                                  A:
                                </span>
                                <span className="flex-1 whitespace-pre-line rounded-xl border border-gray-100 bg-gray-50 px-4 py-3 text-base font-normal leading-relaxed text-gray-800 shadow-inner">
                                  {qa.answer}
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              {/* Ai Assistance Section */}
              {rightTab === "ai" && (
                <div className="mb-4 rounded-2xl border border-blue-200 bg-white p-6 shadow">
                  <h2 className="mb-4 text-xl font-bold text-blue-700">
                    AI Assistance
                  </h2>
                  <AiAssistance />
                </div>
              )}

              {/* Sidebar for small screens: modal with overlay */}
              {isSmallScreen && sidebarOpen && (
                <>
                  // Sidebar modal logic for small screens
                  {/* Overlay */}
                  <div
                    className="bg-black fixed inset-0 z-40 bg-opacity-40"
                    onClick={() => {
                      console.log("[Sidebar] Overlay clicked: closing sidebar");
                      setSidebarOpen(false);
                    }}
                    aria-label="Sidebar overlay"
                  />
                  {/* Sidebar panel */}
                  <div
                    className="fixed right-0 top-0 z-50 h-full w-80 max-w-full bg-white shadow-lg transition-transform duration-300"
                    onClick={(e) => {
                      console.log(
                        "[Sidebar] Clicked inside sidebar panel: stopping propagation"
                      );
                      e.stopPropagation();
                    }}
                    role="dialog"
                    aria-modal="true"
                  >
                    <div className="flex justify-end p-2">
                      <button
                        className="text-gray-500 hover:text-gray-800"
                        onClick={() => {
                          console.log(
                            "[Sidebar] Close button clicked: closing sidebar"
                          );
                          setSidebarOpen(false);
                        }}
                        aria-label="Close sidebar"
                      >
                        <span className="text-2xl">&times;</span>
                      </button>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
          {/* Popup Modal */}
          {subPopup && (
            <div className="fixed inset-0 z-50 flex items-center justify-center">
              <div
                className="bg-black/20 absolute inset-0 backdrop-blur-sm"
                onClick={() => setSubPopup(false)}
              />
              <div className="relative z-50 w-full max-w-md rounded-lg bg-white shadow-xl">
                <div className="p-6">
                  <h2 className="mb-2 flex items-center text-2xl font-bold">
                    <AlertCircle className="mr-2 h-6 w-6 text-yellow-500" />
                    Unlock Profile Confirmation
                  </h2>
                  <p className="mb-4 text-gray-600">
                    Unlocking this profile will deduct 1 credit from your credit
                    balance.
                  </p>
                  <p className="mb-6 flex items-center">
                    Are you sure you want to proceed?
                  </p>
                  <div className="flex justify-end space-x-2">
                    <button
                      className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800"
                      onClick={() => setSubPopup(false)}
                    >
                      <X className="mr-1 h-4 w-4" />
                      Cancel
                    </button>
                    <button className="flex items-center rounded-md bg-brand-600 px-4 py-2 text-white hover:bg-brand-700">
                      <CreditCard className="mr-1 h-4 w-4" />
                      Unlock Now
                    </button>
                  </div>
                </div>
                <button
                  className="absolute right-2 top-2 text-gray-400 hover:text-gray-600"
                  onClick={() => setSubPopup(false)}
                >
                  <X className="h-6 w-6" />
                  <span className="sr-only">Close</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// --- Gemini API Ai Assistance Component ---
const AiAssistance = () => {
  const [input, setInput] = useState("");
  const [response, setResponse] = useState("");
  const [loading, setLoading] = useState(false);

  // Use backend proxy endpoint for Gemini requests
  const GEMINI_API_URL = `${
    import.meta.env.VITE_APP_HOST
  }/api/gemini-assist/assist`;

  const handleSend = async () => {
    if (!input.trim()) return;
    setLoading(true);
    setResponse("");
    try {
      const res = await fetch(GEMINI_API_URL, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ prompt: input }),
      });
      const data = await res.json();
      setResponse(
        data?.candidates?.[0]?.content?.parts?.[0]?.text ||
          "No response from Gemini API."
      );
    } catch (err) {
      setResponse("Error connecting to Gemini API.");
    }
    setLoading(false);
  };

  const handleClear = () => {
    setInput("");
    setResponse("");
  };

  return (
    <div className="mx-auto max-w-xl rounded-2xl border border-blue-300 bg-gradient-to-br from-white to-blue-50 p-6 shadow-lg">
      <div className="mb-4 flex items-center gap-3">
        <span className="text-2xl text-blue-600">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="inline h-7 w-7"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <circle
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="2"
              fill="#e0f2fe"
            />
            <path stroke="#2563eb" strokeWidth="2" d="M8 12h8M12 8v8" />
          </svg>
        </span>
        <label className="text-lg font-bold text-blue-700">
          Ask Gemini AI about this candidate
        </label>
        <button
          className="ml-auto rounded border border-blue-200 px-2 py-1 text-xs text-blue-500 hover:text-blue-700"
          onClick={handleClear}
          disabled={loading && !response}
        >
          Clear
        </button>
      </div>
      <div className="mb-2 flex gap-2">
        <input
          type="text"
          className="flex-1 rounded-lg border-2 border-blue-200 px-4 py-3 text-base transition focus:border-blue-500 focus:outline-none"
          placeholder="E.g. What are this candidate's strengths?"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          disabled={loading}
        />
        <button
          className={`flex items-center gap-2 rounded-lg bg-blue-600 px-5 py-3 font-semibold text-white shadow transition hover:bg-blue-700 ${
            loading || !input.trim() ? "cursor-not-allowed opacity-60" : ""
          }`}
          onClick={handleSend}
          disabled={loading || !input.trim()}
        >
          {loading ? (
            <svg
              className="h-5 w-5 animate-spin text-white"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="white"
                strokeWidth="4"
                fill="none"
              />
              <path
                className="opacity-75"
                fill="white"
                d="M4 12a8 8 0 018-8v8z"
              />
            </svg>
          ) : (
            <>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke="white"
                  strokeWidth="2"
                  d="M5 12h14M12 5l7 7-7 7"
                />
              </svg>
              Ask Gemini
            </>
          )}
        </button>
      </div>
      {response && (
        <div className="mt-4 rounded-xl border border-blue-300 bg-blue-100 p-5 text-blue-900 shadow-inner">
          <div className="mb-2 flex items-center gap-2 font-semibold">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 text-blue-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <circle
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="2"
                fill="#e0f2fe"
              />
              <path stroke="#2563eb" strokeWidth="2" d="M8 12h8M12 8v8" />
            </svg>
            Gemini Response
          </div>
          <div className="whitespace-pre-line text-base">{response}</div>
        </div>
      )}
    </div>
  );
};

export default CandidateProfile;
