# AI-Generated Requirements Implementation Plan

## Executive Summary

This document outlines the implementation of an AI-generated personalized requirements system for intelligent interview evaluation. Unlike predefined parameter templates, this approach dynamically creates assessment criteria tailored to both the job role and candidate's specific background, providing more accurate and personalized evaluation.

## Current System Analysis

### Architecture Overview

The existing interview system follows this flow:
1. **Profile Creation** → 2. **Question Generation** → 3. **Interview Execution** → 4. **Manual Completion** → 5. **Post-Interview Scoring**

### Key Components and Data Flow

#### 1. Video Profile Creation (`videoProfileController.js`)
```javascript
// API: POST /api/v1/create-video-resume
// Input: { candId, jobRole, skills, companyType, experience, salary }
// Process: Creates profile → Generates first question → Stores in database
// Output: { videoProfileId, questions, ... }
```

**Key Files:**
- `visume-api/controllers/videoProfileController.js` (lines 10-198)
- `visume-ui/src/views/candidate/components/CreateVR/CreateVR.jsx` (lines 555-601)

**Data Available for AI Requirements Generation:**
- `candId`: Candidate identifier
- `jobRole`: Target position (e.g., "Frontend Developer")
- `skills`: Array of required skills
- `companyType`: startup/mid_range/mnc
- `experience`: Experience range (0-1, 2-3, 3-5)
- `salary`: Salary expectations
- **Resume Data**: Stored in localStorage as `completeResumeData` (line 565)

#### 2. Question Generation System (`helpers.js`)
```javascript
// Function: generateSingleQuestion()
// Current Logic: AI generates questions based on role, skills, previous Q&A
// Integration Point: Can be enhanced to consider requirements satisfaction
```

**Key Files:**
- `visume-api/utils/helpers.js` (lines 107-275)
- `visume-api/controllers/questionController.js` (lines 3-113)

#### 3. Interview Execution Flow
```javascript
// Frontend: useQuestions.js manages question state and progression
// Backend: /api/v1/generate-question endpoint provides next questions
// Current Logic: Alternates between behavioral/technical questions (5-10 total)
```

**Key Files:**
- `visume-ui/src/views/candidate/hooks/useQuestions.js` (lines 257-331, 333-500)
- `visume-ui/src/views/candidate/components/InterviewSection/InterviewSection.jsx`

#### 4. Answer Analysis System (`helpers.js`)
```javascript
// Function: analyzeAnswerAndGenerateFollowUp()
// Current: Analyzes individual answers, generates follow-up questions
// Integration Point: Perfect place to add requirements checking
```

**Key Files:**
- `visume-api/utils/helpers.js` (lines 30-100)
- `visume-api/controllers/questionController.js` (lines 115-172)

#### 5. Interview Completion
```javascript
// Manual: "Finish Interview" button (minimum 5 questions)
// Automatic: 10 questions maximum limit
// Post-Interview: Score generation via generateScores()
```

**Key Files:**
- `visume-ui/src/views/candidate/smaller_comp/AnswerInput.jsx` (lines 267-286)
- `visume-ui/src/views/candidate/DeviceTest/DeviceTest.jsx` (lines 150-569)

### Current Evaluation System

#### Real-time Evaluation (Limited)
- **Location**: `useQuestions.js` lines 503-551
- **Function**: `calculateScores()` - Basic keyword analysis
- **Scope**: Simple scoring (1-5 scale) based on word count and keywords

#### Post-Interview Evaluation (Comprehensive)
- **Location**: `helpers.js` lines 277-400
- **Function**: `generateScores()` - AI-powered analysis
- **Trigger**: After interview completion
- **Output**: Detailed scores, analysis, suggestions

## AI-Generated Requirements Approach vs. Predefined Parameters

### Comparison Matrix

| Aspect | Predefined Parameters | AI-Generated Requirements |
|--------|----------------------|---------------------------|
| **Personalization** | One-size-fits-all per role | Tailored to candidate background |
| **Flexibility** | Static, requires manual updates | Dynamic, adapts automatically |
| **Scalability** | Limited to configured roles | Works for any role |
| **Accuracy** | Generic assessment | Context-aware evaluation |
| **Maintenance** | High (manual configuration) | Low (AI-driven) |
| **Implementation** | Complex template system | Simpler AI integration |

### Example Comparison

**Predefined Approach:**
```json
{
  "react_proficiency": {
    "threshold": 7.0,
    "criteria": ["Mentions React hooks", "Shows component knowledge"]
  }
}
```

**AI-Generated Approach:**
```json
{
  "requirements": [
    {
      "description": "Demonstrate advanced React patterns suitable for a senior developer with 5 years experience",
      "assessmentCriteria": ["Custom hooks implementation", "Performance optimization", "Architecture decisions"]
    }
  ]
}
```

## Implementation Plan

### Phase 1: AI-Generated Requirements Creation (Week 1-2)

#### Objective
Create personalized evaluation requirements when video profiles are created.

#### Implementation Steps

**Step 1.1: Create Requirements Generation Function**

*File: `visume-api/utils/requirementsGenerator.js`*
```javascript
exports.generatePersonalizedRequirements = async (candidateProfile, jobRole, skills, resumeData) => {
  // AI prompt engineering for personalized requirements
  // Parse and validate AI response
  // Return structured requirements object
}
```

**Step 1.2: Integrate with Video Profile Creation**

*Modify: `visume-api/controllers/videoProfileController.js`*
```javascript
// In createVideoProfile function (after line 76)
const requirements = await generatePersonalizedRequirements({
  candId, jobRole, skills, companyType, experience
}, jobRole, skills, resumeData);

// Store requirements in database alongside profile
```

**Step 1.3: Database Schema Enhancement**

*Add to videoprofile table:*
```sql
ALTER TABLE videoprofile ADD COLUMN requirements LONGTEXT NULL;
```

**Step 1.4: Frontend Integration**

*Modify: `visume-ui/src/views/candidate/components/CreateVR/CreateVR.jsx`*
```javascript
// Include resume data in API payload (line 564)
if (profileData.completeResumeData) {
  apiPayload.resumeData = profileData.completeResumeData;
}
```

#### Deliverables
- Requirements generation function with AI integration
- Enhanced video profile creation with requirements storage
- Database schema update for requirements storage
- Frontend integration for resume data inclusion

#### Testing Requirements
- Unit tests for requirements generation
- Integration tests with video profile creation
- Validation of AI-generated requirements structure
- Performance testing for AI response times

### Phase 2: Dynamic Assessment During Interview (Week 3-4)

#### Objective
Integrate requirements checking into existing answer analysis flow.

#### Implementation Steps

**Step 2.1: Enhance Answer Analysis Function**

*Modify: `visume-api/utils/helpers.js`*
```javascript
// Enhance analyzeAnswerAndGenerateFollowUp function (line 30)
exports.analyzeAnswerAndGenerateFollowUp = async (question, answer, role, skills, previousQA, requirements) => {
  // Existing analysis logic
  
  // NEW: Requirements assessment
  const requirementsAssessment = await assessRequirements(answer, requirements, previousQA);
  
  // Generate follow-up based on unmet requirements
  const followUp = generateRequirementsBasedFollowUp(requirementsAssessment, role);
  
  return {
    analysis: existingAnalysis,
    follow_up: followUp,
    requirementsStatus: requirementsAssessment
  };
}
```

**Step 2.2: Create Requirements Assessment Engine**

*File: `visume-api/utils/requirementsAssessment.js`*
```javascript
exports.assessRequirements = async (answer, requirements, previousQA) => {
  // AI-powered assessment of which requirements are satisfied
  // Track requirement satisfaction across all answers
  // Return updated requirements status
}

exports.checkCompletionStatus = (requirements) => {
  // Determine if all critical requirements are met
  // Return completion recommendation
}
```

**Step 2.3: Integrate with Question Generation**

*Modify: `visume-api/controllers/questionController.js`*
```javascript
// In generateNextQuestion function (line 3)
// Fetch requirements from video profile
const requirements = await getRequirementsForProfile(videoProfileId);

// Check if requirements are satisfied
const completionStatus = checkCompletionStatus(requirements);
if (completionStatus.canComplete && questionCount >= 5) {
  return { success: true, completed: true, autoCompleted: true };
}

// Generate next question targeting unmet requirements
```

**Step 2.4: Frontend Requirements Tracking**

*Create: `visume-ui/src/hooks/useRequirementsTracking.js`*
```javascript
export const useRequirementsTracking = (videoProfileId) => {
  // Track requirements satisfaction in real-time
  // Provide completion status
  // Handle automatic completion triggers
}
```

#### Deliverables
- Enhanced answer analysis with requirements assessment
- Requirements assessment engine with AI integration
- Modified question generation for requirements-based completion
- Frontend requirements tracking system

#### Testing Requirements
- Requirements assessment accuracy testing
- Integration testing with existing answer analysis
- Completion logic validation
- Performance testing for real-time assessment

### Phase 3: Intelligent Interview Completion (Week 5)

#### Objective
Implement automatic interview termination when requirements are satisfied.

#### Implementation Steps

**Step 3.1: Automatic Completion Logic**

*Modify: `visume-ui/src/views/candidate/hooks/useQuestions.js`*
```javascript
// In nextQuestion function (line 333)
const requirementsStatus = await checkRequirementsCompletion(videoProfileId);
if (requirementsStatus.canAutoComplete && questionsAnswered >= 5) {
  // Trigger automatic completion
  await handleAutomaticCompletion();
  return;
}
```

**Step 3.2: Preserve Manual Override**

*Modify: `visume-ui/src/views/candidate/smaller_comp/AnswerInput.jsx`*
```javascript
// Enhance "Finish Interview" button (line 267)
// Add requirements completion indicator
// Maintain existing manual override functionality
```

**Step 3.3: Completion Flow Integration**

*Modify: `visume-ui/src/views/candidate/DeviceTest/DeviceTest.jsx`*
```javascript
// In handleEndInterview function (line 150)
// Add automatic completion path
// Preserve existing post-interview flow
// Include requirements satisfaction in final data
```

#### Deliverables
- Automatic completion logic with requirements checking
- Enhanced manual override with requirements status
- Integrated completion flow with existing post-interview process

#### Testing Requirements
- Automatic completion trigger testing
- Manual override functionality validation
- Post-interview flow compatibility testing
- Edge case handling (partial requirements satisfaction)

## Technical Specifications

### AI Requirements Generation Prompt Structure

```javascript
const requirementsPrompt = `
You are an expert technical recruiter creating personalized interview evaluation requirements.

CANDIDATE PROFILE:
- Role: ${jobRole}
- Skills: ${skills.join(', ')}
- Experience: ${experience}
- Company Type: ${companyType}
- Resume: ${resumeData}

Generate 4-6 specific requirements that are:
1. Tailored to candidate's experience level
2. Relevant to the role and company type
3. Assessable through interview responses
4. Progressive in difficulty

Return JSON format:
{
  "requirements": [
    {
      "id": "req_1",
      "description": "Specific requirement",
      "category": "technical|communication|problem_solving|experience",
      "priority": "high|medium|low",
      "assessmentCriteria": ["criterion1", "criterion2"],
      "satisfied": false
    }
  ],
  "completionThreshold": 0.8,
  "minRequiredSatisfied": 3
}
`;
```

### Requirements Assessment Integration

```javascript
// Enhanced analyzeAnswerAndGenerateFollowUp function
const assessmentResult = {
  analysis: existingAnalysis,
  follow_up: intelligentFollowUp,
  requirementsStatus: {
    totalRequirements: 5,
    satisfiedRequirements: 3,
    completionPercentage: 0.6,
    canAutoComplete: false,
    nextFocusArea: "technical_implementation"
  }
};
```

### Database Schema Changes

```sql
-- Add requirements column to videoprofile table
ALTER TABLE videoprofile ADD COLUMN requirements LONGTEXT NULL;

-- Add requirements tracking table (optional for detailed tracking)
CREATE TABLE interview_requirements (
  id INT AUTO_INCREMENT PRIMARY KEY,
  video_profile_id BIGINT NOT NULL,
  requirement_id VARCHAR(50) NOT NULL,
  description TEXT NOT NULL,
  satisfied BOOLEAN DEFAULT FALSE,
  satisfaction_score DECIMAL(3,2) DEFAULT 0.00,
  evidence TEXT NULL,
  assessed_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (video_profile_id) REFERENCES videoprofile(video_profile_id)
);
```

## Integration Points and Backward Compatibility

### Non-Breaking Integration Strategy

1. **Feature Flag Approach**: Requirements system disabled by default
2. **Graceful Degradation**: Falls back to existing flow if requirements generation fails
3. **Parallel Operation**: Existing evaluation system continues unchanged
4. **Optional Enhancement**: Can be enabled per role or organization

### Preserved Functionality

- Manual "Finish Interview" button remains fully functional
- Existing question generation logic continues to work
- Current scoring and evaluation systems unchanged
- Post-interview flow maintains complete compatibility

### Integration Points

| Component | Integration Method | Backward Compatibility |
|-----------|-------------------|------------------------|
| Video Profile Creation | Add requirements generation | Optional, falls back to existing |
| Question Generation | Enhance with requirements checking | Preserves existing logic |
| Answer Analysis | Extend with requirements assessment | Maintains current analysis |
| Interview Completion | Add automatic triggers | Preserves manual controls |

## Potential Challenges and Mitigation Strategies

### Challenge 1: AI Response Consistency
**Risk**: Variable quality of AI-generated requirements
**Mitigation**: 
- Robust prompt engineering with examples
- Response validation and fallback mechanisms
- Continuous monitoring and improvement

### Challenge 2: Performance Impact
**Risk**: AI calls may slow down interview flow
**Mitigation**:
- Asynchronous processing where possible
- Caching of requirements and assessments
- Timeout handling with graceful degradation

### Challenge 3: Requirements Accuracy
**Risk**: AI may generate inappropriate requirements
**Mitigation**:
- Comprehensive testing with diverse profiles
- Human review and feedback loops
- Configurable requirement templates as fallbacks

### Challenge 4: Integration Complexity
**Risk**: Complex integration with existing system
**Mitigation**:
- Phased implementation approach
- Extensive testing at each phase
- Feature flags for controlled rollout

## Testing and Validation Methodology

### Unit Testing
- Requirements generation function accuracy
- Assessment logic validation
- Completion trigger reliability
- Error handling and fallback mechanisms

### Integration Testing
- End-to-end interview flow with requirements
- Backward compatibility validation
- Performance testing under load
- Cross-browser compatibility

### User Acceptance Testing
- Requirements accuracy for different roles
- Interview completion logic validation
- User experience with automatic completion
- Comparison with manual evaluation results

### A/B Testing Strategy
- Compare AI-generated vs. predefined requirements
- Measure interview completion rates
- Assess candidate satisfaction
- Evaluate hiring manager feedback

## Implementation Timeline

### Week 1-2: Phase 1 - Requirements Generation
- Day 1-3: AI requirements generation function
- Day 4-7: Video profile creation integration
- Day 8-10: Database schema updates
- Day 11-14: Testing and validation

### Week 3-4: Phase 2 - Dynamic Assessment
- Day 15-18: Answer analysis enhancement
- Day 19-22: Requirements assessment engine
- Day 23-26: Question generation integration
- Day 27-28: Frontend tracking system

### Week 5: Phase 3 - Intelligent Completion
- Day 29-31: Automatic completion logic
- Day 32-33: Manual override preservation
- Day 34-35: Integration testing and deployment

### Week 6: Testing and Optimization
- Comprehensive testing across all scenarios
- Performance optimization
- Documentation and training materials
- Production deployment preparation

## Success Metrics

### Technical Metrics
- Requirements generation success rate (>95%)
- Assessment accuracy compared to human evaluation (>85%)
- System performance impact (<200ms additional latency)
- Error rate and fallback frequency (<5%)

### Business Metrics
- Interview completion rate improvement
- Candidate satisfaction scores
- Hiring manager feedback on assessment quality
- Time-to-hire reduction

### Quality Metrics
- Requirements relevance scoring
- Assessment consistency across similar candidates
- False positive/negative rates for completion triggers
- Overall system reliability and uptime

## Detailed Code Implementation Examples

### 1. Requirements Generation Function

```javascript
// visume-api/utils/requirementsGenerator.js
const { GoogleGenerativeAI } = require('@google/generative-ai');

const model = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY).getGenerativeModel({
  model: "gemini-pro"
});

exports.generatePersonalizedRequirements = async (candidateProfile, jobRole, skills, resumeData = '') => {
  try {
    const { candId, experience, companyType } = candidateProfile;

    const prompt = `You are an expert technical recruiter creating personalized interview requirements.

CANDIDATE PROFILE:
- Role Applied For: ${jobRole}
- Required Skills: ${skills.join(', ')}
- Experience Level: ${experience}
- Company Type: ${companyType}
- Resume/Background: ${resumeData || 'Limited information available'}

Generate 4-6 specific, measurable evaluation requirements tailored to this candidate's profile.

Return EXACTLY this JSON structure:
{
  "requirements": [
    {
      "id": "req_technical_1",
      "description": "Demonstrate proficiency in ${skills[0]} with practical examples",
      "category": "technical",
      "priority": "high",
      "assessmentCriteria": [
        "Provides specific code examples or project details",
        "Explains technical concepts clearly",
        "Shows understanding of best practices"
      ],
      "experienceLevel": "${experience}",
      "satisfied": false,
      "satisfactionScore": 0,
      "evidence": []
    }
  ],
  "assessmentStrategy": {
    "completionThreshold": 0.75,
    "minRequiredSatisfied": 3,
    "focusAreas": ["Technical Skills", "Problem Solving", "Communication"]
  },
  "candidateContext": {
    "experienceLevel": "${experience}",
    "targetRole": "${jobRole}",
    "keySkills": ${JSON.stringify(skills)}
  }
}`;

    const result = await model.generateContent(prompt);
    const response = result.response.text();

    return parseAndValidateRequirements(response, candidateProfile);

  } catch (error) {
    console.error('Error generating requirements:', error);
    return createFallbackRequirements(jobRole, skills, experience);
  }
};

const parseAndValidateRequirements = (response, candidateProfile) => {
  try {
    let cleaned = response.trim().replace(/```json\n?/g, '').replace(/```\n?/g, '');
    const parsed = JSON.parse(cleaned);

    // Validate and normalize requirements
    const validatedRequirements = parsed.requirements.map((req, index) => ({
      id: req.id || `req_${index + 1}`,
      description: String(req.description || ''),
      category: req.category || 'technical',
      priority: req.priority || 'medium',
      assessmentCriteria: Array.isArray(req.assessmentCriteria) ? req.assessmentCriteria : [],
      experienceLevel: req.experienceLevel || candidateProfile.experience,
      satisfied: false,
      satisfactionScore: 0,
      evidence: [],
      lastAssessed: null
    }));

    return {
      requirements: validatedRequirements,
      assessmentStrategy: {
        completionThreshold: Number(parsed.assessmentStrategy?.completionThreshold) || 0.75,
        minRequiredSatisfied: Number(parsed.assessmentStrategy?.minRequiredSatisfied) || 3,
        focusAreas: Array.isArray(parsed.assessmentStrategy?.focusAreas) ?
          parsed.assessmentStrategy.focusAreas : ['Technical Skills', 'Communication']
      },
      candidateContext: {
        experienceLevel: parsed.candidateContext?.experienceLevel || candidateProfile.experience,
        targetRole: parsed.candidateContext?.targetRole || candidateProfile.jobRole,
        keySkills: Array.isArray(parsed.candidateContext?.keySkills) ?
          parsed.candidateContext.keySkills : []
      },
      generatedAt: new Date().toISOString(),
      version: '1.0'
    };

  } catch (error) {
    console.error('Error parsing requirements:', error);
    throw new Error(`Failed to parse AI requirements: ${error.message}`);
  }
};
```

### 2. Enhanced Answer Analysis Integration

```javascript
// visume-api/utils/helpers.js - Enhanced analyzeAnswerAndGenerateFollowUp function
exports.analyzeAnswerAndGenerateFollowUp = async (question, answer, role, skills = [], previousQA = [], requirements = null) => {
  try {
    // Existing analysis logic
    const existingAnalysis = await performExistingAnalysis(question, answer, role, skills, previousQA);

    // NEW: Requirements assessment if available
    let requirementsAssessment = null;
    if (requirements && requirements.requirements) {
      requirementsAssessment = await assessAnswerAgainstRequirements(
        answer,
        question,
        requirements.requirements,
        previousQA
      );

      // Update requirements satisfaction status
      requirements.requirements = updateRequirementsSatisfaction(
        requirements.requirements,
        requirementsAssessment
      );
    }

    // Generate intelligent follow-up based on requirements
    const followUp = requirements ?
      generateRequirementsBasedFollowUp(requirementsAssessment, requirements, role) :
      existingAnalysis.follow_up;

    return {
      analysis: existingAnalysis.analysis,
      follow_up: followUp,
      requirementsStatus: requirementsAssessment ? {
        assessedRequirements: requirementsAssessment.length,
        satisfiedCount: requirementsAssessment.filter(r => r.satisfied).length,
        completionPercentage: calculateCompletionPercentage(requirements.requirements),
        canAutoComplete: checkAutoCompletionEligibility(requirements),
        nextFocusArea: identifyNextFocusArea(requirements.requirements)
      } : null
    };

  } catch (error) {
    console.error('Error in enhanced analysis:', error);
    // Fallback to existing analysis
    return await performExistingAnalysis(question, answer, role, skills, previousQA);
  }
};

const assessAnswerAgainstRequirements = async (answer, question, requirements, previousQA) => {
  const assessmentPrompt = `Analyze this interview answer against specific requirements:

QUESTION: "${question}"
ANSWER: "${answer}"

REQUIREMENTS TO ASSESS:
${requirements.map(req => `
- ${req.id}: ${req.description}
  Criteria: ${req.assessmentCriteria.join(', ')}
  Current Status: ${req.satisfied ? 'Satisfied' : 'Not Satisfied'}
`).join('')}

For each requirement, determine:
1. Whether this answer provides evidence for the requirement
2. Satisfaction score (0-10)
3. Specific evidence from the answer
4. Whether the requirement is now satisfied (threshold: 7+)

Return JSON:
{
  "assessments": [
    {
      "requirementId": "req_id",
      "evidenceFound": true/false,
      "satisfactionScore": 7.5,
      "evidence": ["specific quote from answer"],
      "reasoning": "explanation of assessment",
      "satisfied": true/false
    }
  ]
}`;

  try {
    const result = await model.generateContent(assessmentPrompt);
    const response = JSON.parse(result.response.text().replace(/```json\n?/g, '').replace(/```\n?/g, ''));
    return response.assessments || [];
  } catch (error) {
    console.error('Error in requirements assessment:', error);
    return [];
  }
};
```

### 3. Frontend Requirements Tracking Hook

```javascript
// visume-ui/src/hooks/useRequirementsTracking.js
import { useState, useEffect, useCallback } from 'react';

export const useRequirementsTracking = (videoProfileId) => {
  const [requirements, setRequirements] = useState(null);
  const [completionStatus, setCompletionStatus] = useState({
    canAutoComplete: false,
    satisfiedCount: 0,
    totalRequired: 0,
    completionPercentage: 0
  });

  // Load requirements for the video profile
  const loadRequirements = useCallback(async () => {
    try {
      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/video-resume-requirements/${videoProfileId}`
      );
      const data = await response.json();

      if (data.requirements) {
        setRequirements(data.requirements);
        updateCompletionStatus(data.requirements);
      }
    } catch (error) {
      console.error('Error loading requirements:', error);
    }
  }, [videoProfileId]);

  // Update requirements after each answer assessment
  const updateRequirements = useCallback((assessmentResult) => {
    if (!requirements || !assessmentResult.requirementsStatus) return;

    setRequirements(prevReqs => {
      const updatedReqs = { ...prevReqs };

      // Update satisfaction status based on assessment
      if (assessmentResult.requirementsStatus.assessedRequirements) {
        // Update individual requirement satisfaction
        updatedReqs.requirements = updatedReqs.requirements.map(req => {
          const assessment = assessmentResult.requirementsStatus.assessments?.find(
            a => a.requirementId === req.id
          );

          if (assessment) {
            return {
              ...req,
              satisfied: assessment.satisfied,
              satisfactionScore: assessment.satisfactionScore,
              evidence: [...(req.evidence || []), ...(assessment.evidence || [])],
              lastAssessed: new Date().toISOString()
            };
          }
          return req;
        });
      }

      updateCompletionStatus(updatedReqs);
      return updatedReqs;
    });
  }, [requirements]);

  const updateCompletionStatus = (reqs) => {
    if (!reqs || !reqs.requirements) return;

    const totalRequired = reqs.requirements.filter(r => r.priority === 'high').length;
    const satisfiedRequired = reqs.requirements.filter(r => r.priority === 'high' && r.satisfied).length;
    const completionPercentage = totalRequired > 0 ? satisfiedRequired / totalRequired : 0;

    const canAutoComplete = completionPercentage >= (reqs.assessmentStrategy?.completionThreshold || 0.75);

    setCompletionStatus({
      canAutoComplete,
      satisfiedCount: satisfiedRequired,
      totalRequired,
      completionPercentage,
      nextFocusArea: identifyNextFocusArea(reqs.requirements)
    });
  };

  const identifyNextFocusArea = (requirements) => {
    const unsatisfied = requirements.filter(r => !r.satisfied && r.priority === 'high');
    return unsatisfied.length > 0 ? unsatisfied[0].category : null;
  };

  // Check if interview can be automatically completed
  const checkAutoCompletion = useCallback((questionsAnswered) => {
    return questionsAnswered >= 5 && completionStatus.canAutoComplete;
  }, [completionStatus.canAutoComplete]);

  useEffect(() => {
    if (videoProfileId) {
      loadRequirements();
    }
  }, [videoProfileId, loadRequirements]);

  return {
    requirements,
    completionStatus,
    updateRequirements,
    checkAutoCompletion,
    loadRequirements
  };
};
```

## File Modification Summary

### Backend Files to Create/Modify

1. **CREATE**: `visume-api/utils/requirementsGenerator.js`
   - AI-powered requirements generation
   - Validation and fallback logic

2. **CREATE**: `visume-api/utils/requirementsAssessment.js`
   - Requirements assessment engine
   - Completion status checking

3. **MODIFY**: `visume-api/controllers/videoProfileController.js`
   - Add requirements generation to profile creation
   - Store requirements in database

4. **MODIFY**: `visume-api/utils/helpers.js`
   - Enhance `analyzeAnswerAndGenerateFollowUp` function
   - Add requirements assessment integration

5. **MODIFY**: `visume-api/controllers/questionController.js`
   - Add requirements-based completion checking
   - Integrate with question generation logic

6. **CREATE**: `visume-api/routes/requirementsRoutes.js`
   - API endpoints for requirements management

### Frontend Files to Create/Modify

1. **CREATE**: `visume-ui/src/hooks/useRequirementsTracking.js`
   - Requirements state management
   - Completion status tracking

2. **MODIFY**: `visume-ui/src/views/candidate/hooks/useQuestions.js`
   - Integrate requirements tracking
   - Add automatic completion logic

3. **MODIFY**: `visume-ui/src/views/candidate/components/CreateVR/CreateVR.jsx`
   - Include resume data in API calls

4. **CREATE**: `visume-ui/src/components/RequirementsProgress.jsx`
   - Visual progress indicators
   - Requirements status display

5. **MODIFY**: `visume-ui/src/views/candidate/smaller_comp/AnswerInput.jsx`
   - Enhanced "Finish Interview" button
   - Requirements completion indicators

### Database Schema Changes

```sql
-- Add requirements column to videoprofile table
ALTER TABLE videoprofile ADD COLUMN requirements LONGTEXT NULL;

-- Optional: Create detailed requirements tracking table
CREATE TABLE interview_requirements_tracking (
  id INT AUTO_INCREMENT PRIMARY KEY,
  video_profile_id BIGINT NOT NULL,
  requirement_id VARCHAR(50) NOT NULL,
  description TEXT NOT NULL,
  category ENUM('technical', 'communication', 'problem_solving', 'experience', 'behavioral') NOT NULL,
  priority ENUM('high', 'medium', 'low') NOT NULL,
  satisfied BOOLEAN DEFAULT FALSE,
  satisfaction_score DECIMAL(3,2) DEFAULT 0.00,
  evidence JSON NULL,
  last_assessed TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (video_profile_id) REFERENCES videoprofile(video_profile_id) ON DELETE CASCADE,
  INDEX idx_video_profile_requirements (video_profile_id),
  INDEX idx_requirement_status (video_profile_id, satisfied)
);
```

---

**Total Implementation Time**: 6 weeks
**Team Requirements**: 2-3 developers (1 backend, 1 frontend, 1 full-stack)
**Risk Level**: Medium (new AI integration, complex logic)
**Rollback Strategy**: Feature flags allow immediate disable, fallback to existing system
