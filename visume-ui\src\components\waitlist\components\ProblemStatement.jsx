const ProblemStatement = () => {
  return (
    <section id="introduction" className="py-10 lg:py-14 bg-white relative">
      <style>{`
        @import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&family=Sora:wght@100;200;300;400;500;600;700;800&display=swap');
        
        .card-grid {
          display: grid;
          gap: 1.25rem;
          margin-top: 1.5rem;
        }
        @media(min-width: 768px) {
          .card-grid {
            grid-template-columns: repeat(3, 1fr);
          }
        }
        
        .visume-card {
          background: #ffffff;
          border: 2px solid #e0f2fe;
          border-radius: 2rem;
          padding: 0;
          display: flex;
          flex-direction: column;
          position: relative;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          overflow: hidden;
          aspect-ratio: 4/5;
          box-shadow: 0 8px 32px rgba(59, 130, 246, 0.12);
        }
        
        .visume-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(147, 197, 253, 0.04) 100%);
          opacity: 0;
          transition: opacity 0.4s ease;
          z-index: 1;
        }
        
        .visume-card:hover::before {
          opacity: 1;
        }
        
        .visume-card:hover {
          transform: translateY(-12px);
          border-color: #3b82f6;
          box-shadow: 0 24px 48px rgba(59, 130, 246, 0.2);
        }
        
        /* Top Half - Animation Area */
        .animation-half {
          height: 60%;
          width: 100%;
          background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
          position: relative;
          overflow: hidden;
          border-radius: 1.8rem 1.8rem 0 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        /* Bottom Half - Text Area */
        .text-half {
          height: 40%;
          width: 100%;
          padding: 1rem 1.5rem;
          display: flex;
          flex-direction: column;
          justify-content: center;
          position: relative;
          z-index: 2;
          background: white;
        }
        
        /* Card 1 Visual - scattered dots (original) */
        .dots {
          width: 100%;
          height: 100%;
          position: relative;
        }

        .dots::before {
          content: '';
          position: absolute;
          width: 100%;
          height: 100%;
          background-image: radial-gradient(#3b82f6 3px, transparent 4px),
            radial-gradient(#93c5fd 3px, transparent 4px);
          background-size: 25px 25px;
          animation: scatterDots 5s linear infinite alternate;
        }

        @keyframes scatterDots {
          0% { background-position: 0 0; }
          100% { background-position: 25px 25px; }
        }
        
        /* Card 2 Visual - Uneven bar chart */
        .bars {
          display: flex;
          align-items: flex-end;
          justify-content: center;
          gap: 12px;
          padding: 1.75rem;
          height: 100%;
          width: 100%;
        }
        
        .bars span {
          width: 16px;
          background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 100%);
          border-radius: 8px;
          animation: pulseBars 3s ease-in-out infinite;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        
        .bars span:nth-child(1) { height: 25%; animation-delay: 0s; }
        .bars span:nth-child(2) { height: 30%; animation-delay: 0.3s; }
        .bars span:nth-child(3) { height: 85%; animation-delay: 0.6s; }
        .bars span:nth-child(4) { height: 90%; animation-delay: 0.9s; }
        .bars span:nth-child(5) { height: 88%; animation-delay: 1.2s; }
        .bars span:nth-child(6) { height: 20%; animation-delay: 1.5s; }
        .bars span:nth-child(7) { height: 28%; animation-delay: 1.8s; }
        
        @keyframes pulseBars {
          0%, 100% { 
            transform: scaleY(1) scaleX(1);
            opacity: 0.8;
          }
          50% { 
            transform: scaleY(1.4) scaleX(1.1);
            opacity: 1;
          }
        }
        
        /* Card 3 Visual - More rows of blocks */
        .blocks {
          display: grid;
          grid-template-columns: repeat(10, 1fr);
          gap: 3px;
          padding: 0.4rem 1.25rem;
          height: 100%;
          align-content: center;
          width: 100%;
        }
        
        .blocks span {
          height: 10px;
          background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 50%, #93c5fd 100%);
          border-radius: 5px;
          animation: breakBlocks 4s ease-in-out infinite;
          box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
        }
        
        .blocks span:nth-child(odd) {
          animation-delay: 0s;
        }
        .blocks span:nth-child(even) {
          animation-delay: 2s;
        }
        
        @keyframes breakBlocks {
          0%, 100% { 
            transform: translateX(0) scale(1);
            opacity: 1;
          }
          25% { 
            transform: translateX(15px) scale(0.8);
            opacity: 0.7;
          }
          50% { 
            transform: translateX(-10px) scale(1.1);
            opacity: 0.5;
          }
          75% { 
            transform: translateX(8px) scale(0.9);
            opacity: 0.8;
          }
        }
        
        .blue-gradient-text {
          background: linear-gradient(135deg, #3b82f6 0%, #1e40af 50%, #1d4ed8 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
        
        .text-half h3 {
          margin: 0 0 0.5rem 0;
          font-size: 1rem;
          font-weight: 700;
          color: #1f2937;
          line-height: 1.3;
          font-family: 'Manrope', sans-serif;
        }
        
        .text-half p {
          margin: 0;
          font-size: 0.8rem;
          color: #6b7280;
          line-height: 1.5;
          font-weight: 400;
          font-family: 'Sora', sans-serif;
        }
        
        @media (max-width: 768px) {
          .visume-card {
            aspect-ratio: 5/4;
          }
          .animation-half {
            height: 55%;
          }
          .text-half {
            height: 45%;
            padding: 1rem 1.25rem;
          }
          .text-half h3 {
            font-size: 1rem;
          }
          .text-half p {
            font-size: 0.8rem;
          }
        }
      `}</style>

      <div className="max-w-4xl mx-auto px-6">
        {/* Top badge */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center px-6 py-3 bg-blue-500/10 rounded-full border border-blue-200/50">
            <span
              className="text-blue-600 text-sm sm:text-base font-semibold"
              style={{ fontFamily: "Sora, sans-serif" }}
            >
              Why Visume?
            </span>
          </div>
        </div>

        {/* Main heading */}
        <h2
          className="text-center text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 tracking-tight leading-tight mb-3"
          style={{ fontFamily: "Manrope, sans-serif" }}
        >
          Traditional hiring feels <span className="blue-gradient-text">cold and impersonal</span>
        </h2>

        {/* Animated Cards */}
        <div className="card-grid">
          <div className="visume-card">
            <div className="animation-half">
              <div className="dots"></div>
            </div>
            <div className="text-half">
              <h3>
                Resumes feel <span className="blue-gradient-text">robotic</span>
              </h3>
              <p>Static profiles strip away personality — you're just another PDF in a pile.</p>
            </div>
          </div>

          <div className="visume-card">
            <div className="animation-half">
              <div className="bars">
                <span></span>
                <span></span>
                <span></span>
                <span></span>
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
            <div className="text-half">
              <h3>
                Hiring is <span className="blue-gradient-text">inconsistent</span>
              </h3>
              <p>Great candidates get lost in noisy pipelines and automated filtering systems.</p>
            </div>
          </div>

          <div className="visume-card">
            <div className="animation-half">
              <div className="blocks">
                {Array.from({ length: 100 }).map((_, i) => (
                  <span key={i}></span>
                ))}
              </div>
            </div>
            <div className="text-half">
              <h3>
                Culture fit is <span className="blue-gradient-text">invisible</span>
              </h3>
              <p>You can't tell who someone really is by just reading their job titles.</p>
            </div>
          </div>
        </div>

        {/* Final statement */}
        <div className="mt-12 text-center">
          <p
            className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 tracking-tight leading-tight max-w-5xl mx-auto"
            style={{ fontFamily: "Manrope, sans-serif" }}
          >
            We're bringing <span className="blue-gradient-text">authentic human connection</span> back to hiring —
            helping candidates showcase their real potential and enabling recruiters to assess genuine{" "}
            <span className="blue-gradient-text">cultural fit</span>.
          </p>
        </div>
      </div>
    </section>
  )
}

export default ProblemStatement
