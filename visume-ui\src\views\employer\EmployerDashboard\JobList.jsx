import React, { useState, useEffect } from "react";
import PositionsCard from "../components/PositionsCard";
import ProfileCard from "../ProfilesUI/ProfileCard";
import { HiChevronDown, HiChevronUp } from "react-icons/hi";

const JobList = ({ jobData, ProfileSkelLoader }) => {
  const [expandedJobs, setExpandedJobs] = useState({});
  const [matchedProfiles, setMatchedProfiles] = useState({});
  const [loadingProfiles, setLoadingProfiles] = useState({});
  const [errors, setErrors] = useState({});

  const filterMatchingProfiles = (profiles) => {
    if (!profiles || profiles.length === 0) return [];
    return profiles.filter(profile => {
      const score = Math.round(JSON.parse(profile.score)?.score?.Overall_Score) || 0;
      return score >= 50;
    });
  };

  useEffect(() => {
    if (jobData && jobData.length > 0) {
      const matchedProfiles = filterMatchingProfiles(jobData);
      setMatchedProfiles(prev => ({
        ...prev,
        [jobData[0].id]: matchedProfiles
      }));
    }
  }, [jobData]);

  const toggleJobExpansion = (jobId) => {
    setExpandedJobs(prev => ({
      ...prev,
      [jobId]: !prev[jobId]
    }));
  };

  return (
    <div className="col-span-full rounded-2xl bg-white p-6 shadow-lg dark:bg-navy-700 dark:text-white">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="flex flex-row items-center text-lg font-semibold text-gray-800 dark:text-white">
          <span className="mr-2">Active Job Listings</span>
        </h2>
      </div>
      {jobData && jobData.length > 0 && (
        <div className="flex flex-col space-y-4">
          {jobData.map((job) =>
            job.id ? (
              <div key={job.id} className="rounded-lg border border-gray-200">
                <div className="relative">
                  <PositionsCard job={job} />
                  <button
                    onClick={() => toggleJobExpansion(job.id)}
                    className="absolute right-4 top-1/2 -translate-y-1/2 transform rounded-full bg-gray-100 p-2 hover:bg-gray-200 dark:bg-navy-600 dark:hover:bg-navy-500"
                  >
                    {expandedJobs[job.id] ? (
                      <HiChevronUp className="h-5 w-5" />
                    ) : (
                      <HiChevronDown className="h-5 w-5" />
                    )}
                  </button>
                </div>

                {expandedJobs[job.id] && (
                  <div className="border-t border-gray-200 p-4">
                    <h3 className="mb-4 text-md font-semibold">Matching Profiles</h3>
                    {loadingProfiles[job.id] ? (
                      <div className="grid gap-4 md:grid-cols-2">
                        {[1, 2].map((i) => (
                          <ProfileSkelLoader key={i} />
                        ))}
                      </div>
                    ) : errors[job.id] ? (
                      <div className="text-center text-red-500">{errors[job.id]}</div>
                    ) : matchedProfiles[job.id]?.length > 0 ? (
                      <div className="grid gap-4 md:grid-cols-1">
                        {matchedProfiles[job.id]?.map((profile) => (
                          <div key={profile.id} className="bg-gradient-to-br from-white via-gray-50 to-gray-100 border border-gray-200 shadow-lg rounded-xl p-5 transition-all duration-200 hover:shadow-xl">
                            <div className="mb-4">
                              <h4 className="text-lg font-bold text-gray-900 mb-2">{profile.candidateDetails[0].cand_name}</h4>
                              <span className="inline-flex items-center gap-1 rounded-full bg-blue-100 px-3 py-1 text-sm font-semibold text-blue-700 border border-blue-200">
                                <svg className="w-3 h-3 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
                                </svg>
                                {profile.experience_range} years experience
                              </span>
                            </div>
                            <div className="flex flex-wrap gap-2 mb-4">
                              {profile.skills?.split(',').slice(0, 4).map((skill, idx) => (
                                <span key={idx} className="inline-flex items-center gap-1 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 px-3 py-1 text-xs font-semibold text-indigo-700 border border-indigo-200 shadow-sm">
                                  <svg className="w-2 h-2 text-indigo-400" fill="currentColor" viewBox="0 0 20 20">
                                    <circle cx="10" cy="10" r="10" />
                                  </svg>
                                  {skill.trim()}
                                </span>
                              ))}
                              {profile.skills?.split(',').length > 4 && (
                                <span className="inline-flex items-center gap-1 rounded-full bg-gradient-to-r from-gray-100 to-gray-200 px-3 py-1 text-xs font-semibold text-gray-700 border border-gray-200">
                                  +{profile.skills.split(',').length - 4} more
                                </span>
                              )}
                            </div>
                            <a
                              href={`/profile/${profile.video_profile_id}`}
                              className="inline-flex items-center gap-2 px-4 py-2 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold text-sm shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5"
                            >
                              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                              </svg>
                              View Profile
                            </a>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center text-gray-500">
                        No matching profiles found
                      </div>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <ProfileSkelLoader key={`skeleton-${Math.random()}`} />
            )
          )}
        </div>
      )}
    </div>
  );
};

export default JobList;